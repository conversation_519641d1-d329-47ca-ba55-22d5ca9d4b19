$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过
var infringementMap = {
  T1a: '肿瘤侵及固有层或黏膜肌层',
  T1b: '肿瘤侵及黏膜下层',
  T2: '肿瘤侵及固有肌层',
  T3: '肿瘤侵及至浆膜下结缔组织，无内脏腹膜或邻近结构的侵犯',
  T4a: '肿瘤穿透浆膜层 ( 腹膜脏层 ) ，未侵犯邻近结构',
  T4b: '肿瘤侵及邻近结构和器官'
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;

    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      $('[data-type="edit"]').hide()
      $('[data-type="preview"]').show()
      preView()
    } else {
      $('[data-type="edit"]').show()
      $('[data-type="preview"]').hide()
      pageInit()
      if (!isSavedReport) {
        getImpressionText();
      }
      curElem.find('.rt-sr-w').change(function () {
        getImpressionText()
      })
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescription();
  rtStructure.impression = $('#wactdbz-rt-9').val();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function pageInit() {
  editStyle()
  $('input').attr('autocomplete', 'off')
  const controls = [
    // 单纯 checkbox 控制 show-id
    { selector: '#wactdbz-rt-13', show: '[show-id="wactdbz-rt-13"]' },
    { selector: '#wactdbz-rt-160', show: '[show-id="wactdbz-rt-160"]' },
    { selector: '#wactdbz-rt-166', show: '[show-id="wactdbz-rt-166"]' },
    { selector: '#wactdbz-rt-167', show: '[show-id="wactdbz-rt-167"]' },
    { selector: '#wactdbz-rt-168', show: '[show-id="wactdbz-rt-168"]' },
    { selector: '#wactdbz-rt-169', show: '[show-id="wactdbz-rt-169"]' },
    { selector: '#wactdbz-rt-170', show: '[show-id="wactdbz-rt-170"]' },
    { selector: '#wactdbz-rt-171', show: '[show-id="wactdbz-rt-171"]' },
    { selector: '#wactdbz-rt-162', show: '[show-id="wactdbz-rt-162"]' },

    // radio 控制 + 高亮（分型、密度、侵犯、EMVI）
    {
      name: 'size',
      highlightFn: el => el.closest('.size-group-item'),
      special: handleSizeToggle
    },
    {
      name: 'density',
      groupSelector: '.density-group-radio label',
      show: '[show-id="wactdbz-rt-54"]',
      unload: '[unload-id="wactdbz-rt-54"]'
    },
    {
      name: 'infringement',
      groupSelector: '.infringement-item-radio',
      show: '[show-id="wactdbz-rt-90"]'
    },
    {
      name: 'EMVI',
      groupSelector: '.emvi-radio-group-item',
      show: '[show-id="wactdbz-rt-100"]'
    }
  ];

  controls.forEach(cfg => {
    if (cfg.selector) {
      // 简单 checkbox
      $(cfg.selector).on('change', () => toggleShow(cfg));
      toggleShow(cfg);
    } else if (cfg.special) {
      // size 有特殊逻辑
      $('input[name="' + cfg.name + '"]').on('change', cfg.special);
      cfg.special();
    } else {
      // 通用 radio 逻辑
      $('input[name="' + cfg.name + '"]').on('change', () => toggleRadio(cfg));
      toggleRadio(cfg);
    }
  });
}
function genAreaList(str) {
  let arr = str.split(',');
  let start = arr[0];
  let end = arr[1] ? arr[1] : arr[0];
  let result = [];
  for (let i = Number(start); i <= end; i++) {
    result.push(i);
  }
  return result;
}
function preView() {
  editPreviewStyle()
  curElem.find('[data-key]').each(function () {
    let dataKey = $(this).attr('data-key')
    let tempList = []
    let res = []
    let keyList = genAreaList(dataKey)
    keyList.forEach(item => {
      let value = idAndDomMap[`${genPrefixJoinId(item)}`].value
      if (value) {
        res.push(value)
      }
    })
    if (dataKey === '11,23') {
      let children = [25, 27, 28, 29]
      if (res.includes('食管胃结合部')) {
        children.forEach(item => {
          let value = idAndDomMap[`${genPrefixJoinId(item)}`].value
          if (item === 25 && value) {
            tempList.push(`累及食管长度：${value}mm`)
          }
          if (item != 25 && value) {
            tempList.push(`Siewert 分型：${value}`)
          }
        })
        if (tempList.length) {
          res.splice(res.indexOf('食管胃结合部'), 1, '食管胃结合部' + '（' + tempList.join('、') + ')')
        }
      }
      $(this).html(res.join('、'))
      return
    }
    if (dataKey === '35,39' && res.length) {
      if (res.length === 2) {
        res = [res[0], res[1], 'mm,']
      } else {
        res = [res[0], res[1], 'mm×', res[2], 'mm,']
      }
    }
    if (dataKey === '55,58' && res.length) {
      res.unshift('（')
      res.push('）')
    }
    if (dataKey === '78,79' && res.length) {
      if (res.includes('无')) {
        res = ['病灶处胃腔未见狭窄']
      }
    }
    if (dataKey === '80,83' && res.length) {
      res = res.join('、')
      res = `（继发胃腔${res}）`
    }
    if (dataKey === '91,97' && res.length) {
      let idx = res.indexOf('其他')
      if (idx > -1) {
        res.splice(idx, 2, `${res[idx]}${res[idx + 1] ? `（${res[idx + 1]}）` : ''}`)
      }

      res = res.join('、')
      res = `（${res}）`
    }
    if (dataKey === '101,105' && res.length) {
      let idx = res.indexOf('其他')
      if (idx > -1) {
        res.splice(idx, 2, `${res[idx]}${res[idx + 1] ? `（${res[idx + 1]}）` : ''}`)
      }
      res = res.join('、')
    }
    if (dataKey === '107,142' || dataKey === '143,154' || dataKey === '156,158') {
      renderLBJPreview(dataKey, keyList, this)
      return
    }
    if (dataKey === '173' && res.length === 0) {
      res = '无'
    }
    if (dataKey === '162,165' && res.length > 1) {
      res = res[1]
    }
    if (Array.isArray(res)) {
      res = res.join('')
    }
    if (!res && $(this).parent().attr('hide-parent')) {
      $(this).parent().hide()
    }
    $(this).html(res)

  })
}
// 通用：checkbox 控制 单一 show-id
function toggleShow({ selector, show }) {
  $(show).toggle($(selector).is(':checked'));
}

// 通用：radio 控制 + 高亮 + 可选 unload
function toggleRadio({ name, groupSelector, show, unload }) {
  // 高亮
  if (groupSelector) {
    $(groupSelector).css('background-color', '');
    $('input[name="' + name + '"]:checked').each(function () {
      $(this).closest(groupSelector).css('background-color', '#E4E7ED');
    });
  }

  // 显隐
  if (show) {
    const id = $('input[name="' + name + '"]:checked').attr('id');
    $(show).toggle(id === show.match(/"(.+?)"/)?.[1]);
  }
  if (unload) {
    // 对 unload-id 没有特殊逻辑的话也可以 toggle
    $(unload).toggle(!$('input[name="' + name + '"]:checked').length);
  }
}

// 特殊：size 分型的 show/unload + 高亮 + margin 调整
function handleSizeToggle() {
  // 重用高亮逻辑
  $('.size-group-item').css('background-color', '');
  $('input[name="size"]:checked').each(function () {
    $(this).closest('.size-group-item').css('background-color', '#E4E7ED');
  });

  const is31 = $('#wactdbz-rt-31').is(':checked');
  $('[show-id="wactdbz-rt-31"]').toggle(is31);
  $('[unload-id="wactdbz-rt-31"]').toggle(!is31)
    .find('.rt-sr-w').toggleClass('rt-hide', is31);
  $('#wactdbz-rt-40').css('margin-left', is31 ? '0px' : '28px');
}
// 印象推倒
function getImpressionText() {
  let strArr = ['考虑']
  let ctBg = 'ctTxNxMx'
  // 累及部位
  let ljbw = []
  $('input[name="ljbw"]:checked').each(function () {
    ljbw.push($(this).val())
  })
  if (ljbw.includes('食管胃结合部')) {
    let SiewertFS = $('input[name="SiewertFS"]:checked').val()
    if (SiewertFS) {
      ljbw.splice(ljbw.indexOf('食管胃结合部'), 1, `食管胃结合部（Siewert 分型：${SiewertFS}）`)
    }
  }
  strArr.push(ljbw.join('、'))
  // 大小测量
  let size = $('input[name="size"]:checked').val() || ''
  strArr.push(size)
  strArr.push('胃癌可能性大，')
  // 淋巴结
  let Nfengqi = []
  let lbjList = [] // 区域淋巴结
  let lbjListId = []
  let notLbjList = [] // 非区域淋巴结
  $('input[name="Nfengqi"]:checked').each(function () {
    Nfengqi.push($(this).val())
    lbjList.push($(this).val())
    lbjListId.push($(this).attr('id'))
  })
  $('input[name="notNfengqi"]:checked').each(function () {
    Nfengqi.push($(this).val())
    notLbjList.push($(this).val())
  })
  if (Nfengqi.includes('其他')) {
    let NfengqiQt = $('#wactdbz-rt-156').val()
    if (NfengqiQt) {
      Nfengqi.splice(Nfengqi.indexOf('其他'), 1, NfengqiQt)
    }
  }
  if (Nfengqi.length) {
    strArr.push('淋巴结：')
    strArr.push(Nfengqi.join('、'))
    strArr.push('，')
  }
  // 远处转移
  let distantTransfer = []
  $('input[name="distantTransfer"]:checked').each(function () {
    distantTransfer.push($(this).val())
  })
  const transferDetails = [
    { label: '肝脏', textareaId: '#wactdbz-rt-161' },
    { label: '肺部', textareaId: '#wactdbz-rt-180' },
    { label: '骨', textareaId: '#wactdbz-rt-174' },
    { label: '肾上腺', textareaId: '#wactdbz-rt-175' },
    { label: '脑', textareaId: '#wactdbz-rt-176' },
    { label: '腹膜转移', textareaId: '#wactdbz-rt-177' },
    { label: '卵巢', textareaId: '#wactdbz-rt-178' },
    { label: '其他转移', textareaId: '#wactdbz-rt-179' },
  ];

  if (distantTransfer.includes('肺部')) {
    let distantTransferFb = $('input[name="distantTransferFb"]:checked').val()
    if (distantTransferFb) {
      transferDetails[1].label = distantTransferFb
      distantTransfer.splice(distantTransfer.indexOf('肺部'), 1, distantTransferFb)
    }
  }
  // transferDetails.forEach(({ label, textareaId }) => {
  //   if (distantTransfer.includes(label)) {
  //     const val = $(textareaId).val();
  //     if (val) {
  //       distantTransfer.splice(distantTransfer.indexOf(label), 1, `${label}(${val})`);
  //     }
  //   }
  // });
  // if (distantTransfer.includes('肺部')) {
  //   let distantTransferFb = $('input[name="distantTransferFb"]:checked').val()
  //   if (distantTransferFb) {
  //     distantTransfer.splice(distantTransfer.indexOf('肺部'), 1, `${distantTransferFb}`)
  //   }
  // }

  if (distantTransfer.length) {
    strArr.push('远处转移：')
    strArr.push(distantTransfer.join('、'))
    strArr.push('，')
  }

  // 推算影像分期
  ctBg = updateTStaging(ctBg)
  ctBg = updateNStaging(ctBg, lbjList, lbjListId)
  ctBg = updateMStaging(ctBg, distantTransfer, notLbjList)
  strArr.push(`影像分期：${ctBg}`)
  let EMVI = $('input[name="EMVI"]:checked').val()
  if (EMVI.includes('阴性')) {
    strArr.push('，EMVI（-）')
  } else {
    strArr.push('，EMVI（+）')
  }
  let result = strArr.join('')
  $('#wactdbz-rt-9').val(result)
  return result
}
function render3SizeLBJ(to2DArr, flag) {
  let appendValue = to2DArr.map(item => {
    let res = item.map((item2, index) => {
      let value = idAndDomMap[genPrefixJoinId(item2)].value
      if (value) {
        if (index === 0) {
          return `<span>${value}</span>`
        }
        if (index === 1) {
          return `<span>${value}枚</span>`
        }
        if (index === 2) {
          return `<span>最大短径：${value}mm</span>`
        }
      }
    }).filter(Boolean).join('，')
    if (res) {
      return `<div>${res}</div>`
    } else {
      return ''
    }
  }).join('')
  if (!appendValue) {
    return ''
  }
  return `
  <div class="flex">
    <span class="lbj-preview-label">${flag === 1 ? '区域淋巴分区：' : flag === 2 ? '非区域淋巴分区：' : '其他：'}</span>
    <div class="f-1 flex-column">
    ${appendValue}
    </div>
  </div>
  `
}
function renderLBJPreview(dataKey, keyList, dom) {
  let temp = ''
  let to2DArr = arrTo2DArray(keyList, 3)
  if (dataKey === '107,142') {
    temp = render3SizeLBJ(to2DArr, 1)
  }
  if (dataKey === '143,154') {
    temp = render3SizeLBJ(to2DArr, 2)
  }
  if (dataKey === '156,158') {

    temp = render3SizeLBJ(to2DArr, 3)
  }
  $(dom).html(temp)
}
// 推算T分期
function updateTStaging(ctBg) {
  let infringement = $('input[name="infringement"]:checked').val()
  if (infringement) {
    Object.entries(infringementMap).forEach(([key, value]) => {
      if (value === infringement) {
        ctBg = ctBg.replace('Tx', key)
      }
    })
  }
  return ctBg
}
// 推算N分期：
function updateNStaging(ctBg, Nfengqi, lbjListId) {
  let count = 0
  lbjListId.forEach(item => {
    let val = Number($(`[pid="${item}"][placeholder="数量"]`).val())
    if (val) {
      count += val
    }
  })
  // const count = Nfengqi.length;
  if (count === 0) {
    ctBg = ctBg.replace('Nx', 'N0');
  } else if (count <= 2) {
    ctBg = ctBg.replace('Nx', 'N1');
  } else if (count <= 6) {
    ctBg = ctBg.replace('Nx', 'N2');
  } else if (count >= 7) {
    // 先替换为 N3（大分类）
    ctBg = ctBg.replace('Nx', 'N3');

    // 再细分为 N3a 或 N3b
    if (count <= 15) {
      ctBg = ctBg.replace('N3', 'N3a');
    } else {
      ctBg = ctBg.replace('N3', 'N3b');
    }
  }
  return ctBg;
}
// 推算M分期
function updateMStaging(ctBg, distantTransfer, notLbjList) {
  if (notLbjList.length) {
    distantTransfer = distantTransfer.concat([notLbjList])
  }
  /**
  * M分期：
   Mx 远处转移不能被确定

   M0无远处转移

   M1有远处转移（分为M1a，M1b和M1c）

   M1a远处转移局限于单个器官（如肝，肺，卵巢，非区域淋巴结），但没有腹膜转移

   M1b远处转移分布于1个以上的器官

   M1c腹膜转移有或没有其他器官转移
 */
  if (distantTransfer.length === 0) {
    ctBg = ctBg.replace('Mx', 'M0');
  } else {
    if (distantTransfer.includes('腹膜转移') && distantTransfer.length === 1) {
      ctBg = ctBg.replace('Mx', 'M1c');
    } else {
      if (distantTransfer.length === 1) {
        ctBg = ctBg.replace('Mx', 'M1a')
      } else {
        ctBg = ctBg.replace('Mx', 'M1b')
      }
    }
  }

  return ctBg
}
function editPreviewStyle() {
  $('.impression-label').addClass('impression-label-preview')
  $('.container-footer').addClass('container-footer-preview')
  $('.form-item-label').addClass('form-item-label-preview')
  $('.footer-impression').addClass('footer-impression-preview')
}
function editStyle() {
  $('.impression-label').removeClass('impression-label-preview')
  $('.container-footer').removeClass('container-footer-preview')
  $('.form-item-label').removeClass('form-item-label-preview')
  $('.footer-impression').removeClass('footer-impression-preview')
}
function genPrefixJoinId(idNum, flag) {
  let preFix = (flag ? '#' : '') + 'wactdbz-rt-'
  return preFix + idNum
}

// 将一个数组分成三个为一组的二维数组
function arrTo2DArray(arr, size) {
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
}
function getDescription() {
  let result = []
  let lbjw = getVal('[name="ljbw"]:checked') || ''
  function getLbjwDetail() {
    if (lbjw.includes('食管胃结合部')) {
      let result = []
      let long = getVal('#wactdbz-rt-25')
      let siewertFx = getVal('[name="SiewertFS"]:checked')
      if (long) {
        result.push(`累及食管长度：${long}mm`)
      }
      if (siewertFx) {
        result.push(`Siewert 分型：${siewertFx}`)
      }
      if (result.length) {
        return result.join('，') + '，'
      }
    }
    return ''
  }
  function getSizeHandler() {
    let size = getVal('[name="size"]:checked')
    let result = []
    let hddx = getVal('#wactdbz-rt-36') || ''
    let hddx2 = getVal('#wactdbz-rt-37') || ''
    let zdhd = getVal('#wactdbz-rt-39')
    let gzw = getVal('#wactdbz-rt-41')
    // if(size){
    //   result.push(size)
    // }
    if (size === '肿块型（Borrmann分型:Ⅰ型）') {
      if (hddx || hddx2) {
        result.push(`最大横断面大小约：${hddx}mm×${hddx2}mm`)
      }
    } else {
      if (zdhd) {
        result.push(`最大横断面的胃壁厚度：${zdhd}mm`)
      }
    }
    if (gzw) {
      result.push(`冠状位上下端长径：${gzw}mm`)
    }
    if (result.length) {
      return result.join('，') + '，'
    }
    return ''
  }
  function getBzScan() {
    let psmd = getVal('[name="flatScanDensity"]:checked')
    let mdqk = getVal('[name="density"]:checked')
    let result = []
    if (psmd) {
      result.push(`病灶平扫呈${psmd}`)
    }
    if (mdqk) {
      if (mdqk === '均匀') {
        result.push(`密度${mdqk}`)
      } else {
        let text = ''
        let mdqkDetail = getVal('[name="mdbjy"]:checked')
        text = '密度不均匀'
        if (mdqkDetail) {
          if (mdqkDetail === '其他') {
            text += `（${getVal('#wactdbz-rt-58')}）`
          } else {
            text += `（${mdqkDetail}）`
          }
        }
        result.push(text)
      }
    }
    if (result.length) {
      return result.join('，') + '。'
    }
    return ''
  }
  // 获取增强内容
  function getZQList() {
    let enhancement = getVal('[name="enhancement"]:checked')
    let result = []
    let subResult = []
    if (enhancement) {
      result.push(`动脉晚期${enhancement}`)
    }
    let mjmq = getVal('[name="mjmq"]:checked')
    let ycq = getVal('[name="ycq"]:checked')

    if (mjmq && ycq && mjmq === ycq) {
      subResult.push(`门静脉期`)
      subResult.push(`延迟期${mjmq}`)
      result.push(subResult.join('、'))
      return '增强' + result.join('，') + '。'
    }
    if (mjmq) {
      result.push(`门静脉期${mjmq}`)
    }
    if (ycq) {
      result.push(`延迟期${ycq}`)
    }
    if (result.length) {
      return '增强' + result.join('，') + '。'
    }
    return ''
  }
  // 病灶处
  function getBzc() {
    let lesion_widen_gastric_cavity = getVal('[name="lesion_widen_gastric_cavity"]:checked')
    let result = []
    if (!lesion_widen_gastric_cavity) return ''
    if (lesion_widen_gastric_cavity === '无') return '病灶处胃腔未见狭窄。'
    let lesion_widen_gastric_cavity_item = getVal('[name="lesion_widen_gastric_cavity_item"]:checked')
    if (lesion_widen_gastric_cavity_item) {
      result.push(`病灶处胃腔狭窄，继发胃腔${lesion_widen_gastric_cavity_item}`)
    }
    if (result.length === 0) {
      return '病灶处胃腔狭窄。'
    }
    return result.join() + '。'
  }
  function getThird() {
    let infringement = getVal('[name="infringement"]:checked')
    if (!infringement) return ''
    let result = ''
    Object.entries(infringementMap).forEach(([key, value]) => {
      if (value === infringement) {
        if (key === 'T4b') {
          let T4aChild = getVal('[name="T4aChild"]:checked')
          let text = `${key}：（${T4aChild}）`
          let value97 = getVal('#wactdbz-rt-97')
          if (value97 && text.includes('其他')) {
            text = text.replace('其他', value97)
          }
          result = text + '。'
        } else {
          result = `${key}：${value}` + '。'
        }
      }
    })
    return '　　' + result
  }
  function getFourth() {
    let EMVI = getVal('[name="EMVI"]:checked')
    if (!EMVI) return ''
    let result = '　　EMVI：'
    if (EMVI === '阴性（-）') {
      return result + EMVI + '。'
    }
    let emviyx = getVal('[name="emviyx"]:checked')
    if (!emviyx) {
      return result + EMVI + '。'
    }
    if (emviyx === '其他') {
      let value105 = getVal('#wactdbz-rt-105')
      if (value105) {
        return result + value105 + '。'
      }
    }
    return result + emviyx + '。'
  }
  function getFifth() {
    const Nfengqi = getVal('[name="Nfengqi"]:checked');
    if (!Nfengqi) return '';

    // 通用：根据 baseIds(逗号ID串) 和 prefixChecked 构建行数据 [label, num, size]
    function collectRows(baseIds, prefixChecked, checkedSuffix = false) {
      return arrTo2DArray(genAreaList(baseIds), 3)
        .map(([labelId, numId, sizeId]) => {
          const labelVal = getVal(genPrefixJoinId(labelId, prefixChecked) + (checkedSuffix ? ':checked' : ''));
          if (!labelVal) return false;
          const numVal = getVal(genPrefixJoinId(numId, prefixChecked));
          const sizeVal = getVal(genPrefixJoinId(sizeId, prefixChecked));
          return [labelVal, numVal, sizeVal];
        })
        .filter(Boolean);
    }

    // 区域淋巴结
    const areaRows = collectRows('107,142', true, true);
    const segments = [];

    if (areaRows.length) {
      segments.push(formatRows(areaRows, '可疑区域淋巴结转移'));
    }

    // 非区域淋巴结，基础 143,154
    let notAreaRows = collectRows('143,154', true, true);

    // 如果 155.checked，则补充 156,158 的输入框（不带 :checked）
    if (getVal('#wactdbz-rt-155:checked')) {
      notAreaRows = notAreaRows.concat(
        collectRows('156,158', true, false)
      );
    }

    if (notAreaRows.length) {
      segments.push(formatRows(notAreaRows, '可疑非区域淋巴结转移'));
    }

    // 没有任何 segment，直接返回空
    if (!segments.length) return '';

    // 拼接最终文本
    return `　　淋巴结：${segments.join('；')}。`;
  }

  // 辅助：给一组 rows 数据生成文字
  function formatRows(rows, title) {
    // rows 是 [ [label, num, size], ... ]
    let text = `${rows.length}枚${title}，${rows.length > 1 ? '分别' : ''}位于`;
    rows.forEach((cols, idx) => {
      const parts = [];
      if (cols[0]) parts.push(cols[0]);
      if (cols[1]) parts.push(`${cols[1]}枚`);
      if (cols[2]) parts.push(`最大短径${cols[2]}mm`);
      text += parts.join('，');
      if (idx < rows.length - 1) text += '，';
    });
    return text;
  }

  function getSixth() {
    // 远处转移
    let result = []
    let distantTransfer = []
    $('input[name="distantTransfer"]:checked').each(function () {
      distantTransfer.push($(this).val())
    })


    const transferDetails = [
      { label: '肝脏', textareaId: '#wactdbz-rt-161' },
      { label: '肺部', textareaId: '#wactdbz-rt-180' },
      { label: '骨', textareaId: '#wactdbz-rt-174' },
      { label: '肾上腺', textareaId: '#wactdbz-rt-175' },
      { label: '脑', textareaId: '#wactdbz-rt-176' },
      { label: '腹膜转移', textareaId: '#wactdbz-rt-177' },
      { label: '卵巢', textareaId: '#wactdbz-rt-178' },
      { label: '其他转移', textareaId: '#wactdbz-rt-179' },
    ];
    transferDetails.forEach(({ label, textareaId }) => {
      const val = $(textareaId).val();
      if (val) {
        distantTransfer.splice(distantTransfer.indexOf(label), 1, val)
      } else {
        if (distantTransfer.includes(label)) {
          distantTransfer.splice(distantTransfer.indexOf(label), 1)
        }
        // distantTransfer.splice(distantTransfer.indexOf(label), 1)
      }
      // if (distantTransfer.includes(label) && label === '其他转移') {

      // } else {
      //   if (distantTransfer.includes(label)) {
      //     const val = $(textareaId).val();
      //     if (val) {
      //       distantTransfer.splice(distantTransfer.indexOf(label), 1, `${label}${val}`);
      //     }else{
      //       distantTransfer.splice(distantTransfer.indexOf(label), 1)
      //     }
      //   }
      // }
    });

    // { label:'肺部', textareaId:'#wactdbz-rt-180' },
    // if (distantTransfer.includes('肺部')) {
    //   let distantTransferFb = $('input[name="distantTransferFb"]:checked').val()
    //   const val = $('#wactdbz-rt-180').val() || '';
    //   if (distantTransferFb) {
    //     transferDetails[1].label = distantTransferFb
    //     distantTransfer.splice(distantTransfer.indexOf('肺部'), 1, distantTransferFb + val)
    //   } else {
    //     distantTransfer.splice(distantTransfer.indexOf('肺部'), 1, '肺部' + val)
    //   }
    // }
    if (distantTransfer.length) {
      result.push('　　远处转移：')
      result.push(distantTransfer.join('，'))
      result.push('。')
    }
    if (result.length) {
      return result.join('')
    }
    return ''
  }
  function getSeventh() {
    let result = '';
    let val = getVal('#wactdbz-rt-173')
    if (val) {
      result = `　　其他征象：${val}。`
    }
    return result
  }
  // 第二
  let second = `　　原发灶：${lbjw}见${getVal('[name="shape"]:checked')}病灶,${getLbjwDetail()}${getSizeHandler()}${getBzScan()}${getZQList()}${getBzc()}`
  // 第三
  let third = getThird()
  // 第四
  let fourth = getFourth()
  // 第五
  let fifth = getFifth()
  // 第六
  let sixth = getSixth()
  // 第七
  let seventh = getSeventh()
  if (getVal('[name="wztpg"]:checked')) {
    result.push(`　　${getVal('[name="wztpg"]:checked')}。`)
  }
  result.push(second)
  if (third) {
    result.push(third)
  }
  if (fourth) {
    result.push(fourth)
  }
  if (fifth) {
    result.push(fifth)
  }
  if (sixth) {
    result.push(sixth)
  }
  if (seventh) {
    result.push(seventh)
  }

  return result.join('\n')
}