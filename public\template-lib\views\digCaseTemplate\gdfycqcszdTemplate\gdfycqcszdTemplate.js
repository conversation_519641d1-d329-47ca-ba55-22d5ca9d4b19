$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#fzblyzdd1', 'edit');
  // initHtmlScript('#fzblyzdd1', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
      dignosisImgs: [],
      asyncAjax: true
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initPage(type) {
  setTimeout(() => {
    showData(type);
  });
}

function showData(type) {
  new Vue({
    el: '#data-wrap',
    data() {
      return {
        isEdit: type === 'edit',
        chartNameList: ['BPD', 'HC', 'AC', 'FL'],
        paramCodeList: [],
        formulaIdList: [
          {formulaId: '10001', name: '双顶径(BPD)'},
          {formulaId: '10002', name: '头围(HC)'},
          {formulaId: '10003', name: '腹围(AC)'},
          {formulaId: '10004', name: '股骨长(FL)'},
        ], // 参数id
        publicInfo: publicInfo || {}, // { patLocalId, examNo, ... }
        currDataList: [], // 当前检查数据列表
        currData: {},
        pregnancyWeeks: '', // 当前孕周
        searchType: 'sickId', // 查询类型 sickId identityCard
        searchNameArr: [], // 查询参数名称
        searchParams: {}, // 查询入参记录
        historyDataList: [], // 查询历史结果
        chartMap: {},
        checkedList: [], // 已选图表
        $save: null,
      }
    },
    created() {
      // this.publicInfo.examNo = '0003363700';
      this.getParamCodeList();
    },
    mounted() {
      this.$save = document.getElementById('saveData');

      let inst = this;
      $('.check-handle').on('change', function() {
        let val = this.value;
        let arr = val.split(',');
        arr.forEach(v => {
          $('.check-handle[value="'+ v +'"]')[0].checked = this.checked;
          let idx = inst.checkedList.indexOf(v);
          if (this.checked) {
            if (idx === -1) {
              inst.checkedList.push(v);
            }
          } else {
            if (idx !== -1) {
              inst.checkedList.splice(idx, 1);
            }
          }
        });
        if (inst.checkedList.length === 4) {
          $('.check-handle-all')[0].checked = true;
        } else {
          $('.check-handle-all')[0].checked = false;
        }
        inst.saveParams();
      });
      if (this.isEdit) {
        this.previewData();
        this.getCurData();
        this.searchHisParamsReq();
        window.postMessage({
          message: 'finishAsyncAjax',
          data: {}
        }, '*');
      } else {
        this.previewData();
        this.getCurData();
        this.searchHisParamsReq();
        this.addDataToChart();
        this.getChartImgs();
        window.postMessage({
          message: 'finishAsyncAjax',
          data: {}
        }, '*');
      }
    },
    methods: {
      // 获取图表
      getChartImgs() {
        let dignosisImgs = [];
        this.chartNameList.forEach(k => {
          if (!this.checkedList.includes(k)) {
            return;
          }
          dignosisImgs.push({
            imgName: this.chartMap[k].name + '.jpg',
            imgContent: this.chartMap[k].chart.getDataURL({pixelRatio: 2, backgroundColor: '#fff'})
          });
        });
        if (rtStructure) {
          rtStructure.diffConfig.dignosisImgs = dignosisImgs;
        }
      },
      // 回显
      previewData() {
        let params = this.$save.innerText;
        if (params) {
          params = JSON.parse(params);
          this.checkedList = params.checkedList || [];
          this.searchParams = params.searchParams || {};
          this.searchType = params.searchType || 'sickId';
          this.searchNameArr = params.searchNameArr || [];
        }
      },
      // 保存参数
      saveParams() {
        this.$save.innerText = JSON.stringify({
          searchType: this.searchType,
          searchNameArr: this.searchNameArr,
          searchParams: this.searchParams,
          checkedList: this.checkedList,
        });
      },
      // 获取当前数据
      getCurData() {
        if (!this.publicInfo.examNo) {
          this.$message.error('没有检查号');
          return;
        }
        let list = this.getExamMeasureParamList({examNo: this.publicInfo.examNo});
        if (list.length === 0) {
          return;
        }
        list = dealListData(list);
        this.currDataList = list;
        this.currData = list[0];
        this.isEdit && this.addDataToChart();
      },
      // 获取历史数据
      searchHisParams() {
        if (!this.publicInfo.examNo) {
          this.$message.error('没有检查号');
          return;
        }
        let params = {
          currentExamNo: this.publicInfo.examNo,
          paramCodes: this.searchNameArr.join(','),
        };
        if (this.searchType === 'sickId') {
          params.sickId = this.publicInfo.sickId || '';
          if (!params.sickId) {
            params.sickIdType = '0';
          }
        }
        if (this.searchType === 'identityCard') {
          params.identityCard = this.publicInfo.identityCard || '';
          if (!params.identityCard) {
            params.identityCardTypy = '0';
          }
        }
        this.searchParams = params;
        this.saveParams();
        this.searchHisParamsReq();
      },
      searchHisParamsReq() {
        if (!this.publicInfo.examNo) {
          this.$message.error('没有检查号');
          return;
        }
        if (!this.searchParams.currentExamNo) {
          return;
        }
        let list = this.getExamMeasureParamList(this.searchParams);
        if (list.length === 0) {
          this.$message.error('查询不到该患者的历史数据');
        }
        list = dealListData(list);
        this.historyDataList = list;
        this.isEdit && this.addDataToChart();
      },
      getExamMeasureParamList(params) {
        return window.getExamMeasureParamList(params);
      },
      getParamCodeList() {
        let list = window.getParamCodeList();
        this.paramCodeList = list;
      },
      // 将实际数据添加到列表
      addDataToChart() {
        let list = [...this.currDataList, ...this.historyDataList];
        let BPD_Data = [];
        let HC_Data = [];
        let AC_Data = [];
        let FL_Data = [];
        list.forEach(({formulaId, chartX, chartY}) => {
          switch (formulaId) {
            case '10001':
              BPD_Data.push([chartX, chartY]);
              break;
            case '10002':
              HC_Data.push([chartX, chartY]);
              break;
            case '10003':
              AC_Data.push([chartX, chartY]);
              break;
            case '10004':
              FL_Data.push([chartX, chartY]);
              break;
          }
        });
        this.chartMap['BPD'] = {name: '双顶径(BPD)', chart: makeBPDChart(BPD_Data)};
        this.chartMap['HC'] = {name: '头围(HC)', chart: makeHCChart(HC_Data)};
        this.chartMap['AC'] = {name: '腹围(AC)', chart: makeACChart(AC_Data)};
        this.chartMap['FL'] = {name: '股骨长(FL)', chart: makeFLChart(FL_Data)};
      },
    },
  });
}

// 处理列表数据
function dealListData(list) {
  list.forEach(row => {
    row.examDateTime = (row.examDate || '') + ' ' + (row.examTime || '');
    let pregnancyWeeks = row.pregnancyWeeks || '';
    if (!pregnancyWeeks) {
      return;
    }
    let wPattern = pregnancyWeeks.match(/\d+w/);
    if (!wPattern) {
      wPattern = pregnancyWeeks.match(/\d+周/);
    }
    let dPattern = pregnancyWeeks.match(/\d+d/);
    if (!dPattern) {
      dPattern = pregnancyWeeks.match(/\d+天/);
    }
    let week, day;
    if (wPattern) {
      week = wPattern[0].replace(/[w周]/, '');
    }
    if (dPattern) {
      day = dPattern[0].replace(/[d天]/, '');
    }
    if (!week && !day) {
      return;
    }
    week = week ? Number(week) : 0;
    day = day ? Number(day) : 0;
    let weekNum = (week + day / 7).toFixed(5);
    weekNum = Number(weekNum);
    let chartX = weekNum;
    let chartY = row.paramValue || 0;
    let val, sd;
    switch (row.formulaId) {
      case '10001': // 图表纵坐标为mm
        val = getBPD(weekNum);
        sd = getBPD_SD(weekNum);
        if (row.unit === 'cm') {
          chartY = chartY * 10;
        }
        break;
      case '10002': // 图表纵坐标为cm
        val = getHC(weekNum);
        sd = getHC_SD(weekNum);
        if (row.unit === 'mm') {
          chartY = chartY / 10;
        }
        break;
      case '10003': // 图表纵坐标为cm
        val = getAC(weekNum);
        sd = getAC_SD(weekNum);
        if (row.unit === 'mm') {
          chartY = chartY / 10;
        }
        break;
      case '10004': // 图表纵坐标为mm
        val = getFL(weekNum);
        sd = getFL_SD(weekNum);
        if (row.unit === 'cm') {
          chartY = chartY * 10;
        }
        break;
    }
    row.chartX = chartX;
    row.chartY = chartY;
    row.pregnancyWeeksText = week + '周' + day + '天';
    if (!sd) {
      return;
    }
    if (row.unit === 'mm') {
      val = val * 10;
      sd = sd * 10;
    }
    let sd2m = val - sd * 2;
    let sd2p = val + sd * 2;
    if (row.paramValue < sd2m) {
      row.valMark = '↓';
    }
    if (row.paramValue > sd2p) {
      row.valMark = '↑';
    }
    row.sd2 = sd2m.toFixed(1) + ' - ' + sd2p.toFixed(1);
  });
  return list;
}

// 公式计算的单位都是cm
function getBPD(GA) {
  return -1.295192 + 0.197042 * GA + 0.008247 * GA * GA - 0.000163 * GA * GA * GA;
}
function getHC(GA) {
  return -5.530556 + 0.766353 * GA + 0.030694 * GA * GA - 0.000643 * GA * GA * GA;
}
function getAC(GA) {
  return -6.181539 + 0.884154 * GA + 0.013282 * GA * GA - 0.000255 * GA * GA * GA;
}
function getFL(GA) {
  return -4.445082 + 0.492073 * GA - 0.0067 * GA * GA + 0.000042 * GA * GA * GA;
}
function getBPD_SD(GA) {
  return 1.253 * (0.176837 + 0.002714 * GA);
}
function getHC_SD(GA) {
  return 1.253 * (0.539913 + 0.007092 * GA);
}
function getAC_SD(GA) {
  return 1.253 * (0.056796 + 0.031209 * GA);
}
function getFL_SD(GA) {
  return 1.253 * (0.103184 + 0.002539 * GA);
}

// 绘制BPD图表
function makeBPDChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 12; GA <= 40; GA++) {
    let v = getBPD(GA);
    let sd = getBPD_SD(GA);
    v = v * 10;
    sd = sd * 10;
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-bpd',
    xName: '胎龄(w)',
    yName: 'BPD(mm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 110,
    intervalY: 10,
    intervalYsub: 2,
    sd2p,
    sd0,
    sd2m,
    trueData
  });
}

// 绘制HC图表
function makeHCChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 15; GA <= 42; GA++) {
    let v = getHC(GA);
    let sd = getHC_SD(GA);
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-hc',
    xName: '胎龄(w)',
    yName: 'HC(cm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 45,
    intervalY: 5,
    intervalYsub: 1,
    sd2p,
    sd0,
    sd2m,
    trueData
  });
}

// 绘制AC图表
function makeACChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 15; GA <= 42; GA++) {
    let v = getAC(GA);
    let sd = getAC_SD(GA);
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-ac',
    xName: '胎龄(w)',
    yName: 'AC(cm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 45,
    intervalY: 5,
    intervalYsub: 1,
    sd2p,
    sd0,
    sd2m,
    trueData
  });
}

// 绘制FL图表
function makeFLChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 15; GA <= 42; GA++) {
    let v = getFL(GA);
    let sd = getFL_SD(GA);
    v = v * 10;
    sd = sd * 10;
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-fl',
    xName: '胎龄(w)',
    yName: 'FL(mm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 90,
    intervalY: 10,
    intervalYsub: 2,
    sd2p,
    sd0,
    sd2m,
    trueData
  });
}

function drawChart(setting) {
  let myChart = echarts.init(document.getElementById(setting.id));
  
  // 指定图表的配置项和数据
  let options = {
    animation: false,
    grid: {
      left: 50,
      bottom: 44,
      right: 20,
      top: 15
    },
    xAxis: [
      {
        zlevel: 1,
        name: setting.xName,
        nameLocation: 'middle',
        nameTextStyle: {
          color: '#000'
        },
        nameGap: 26,
        min: setting.minGA,
        max: setting.maxGA,
        interval: setting.intervalX,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#1885F2',
          showMinLabel: true,
          showMaxLabel: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#76B0F9'
          }
        },
      },
      {
        zlevel: 0,
        min: setting.minGA,
        max: setting.maxGA,
        interval: 1,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#D3EEF9'
          }
        }
      }
    ],
    yAxis: [
      {
        zlevel: 1,
        name: setting.yName,
        nameLocation: 'middle',
        nameTextStyle: {
          color: '#000'
        },
        nameGap: 30,
        min: setting.minY,
        max: setting.maxY,
        interval: setting.intervalY,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#1885F2',
          showMinLabel: true,
          showMaxLabel: true
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#76B0F9'
          }
        }
      },
      {
        zlevel: 0,
        min: setting.minY,
        max: setting.maxY,
        interval: setting.intervalYsub,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#D3EEF9'
          }
        }
      }
    ],
    series: [
      {data: setting.sd2p, name: '+2SD', lineStyle: {color: '#945FB9', width: 1}, endLabel: {color: '#945FB9', show: true, formatter: '{a}', distance: 1}, silent: true, type: 'line', smooth: true, symbol: 'none', zlevel: 2},
      {data: setting.sd0, name: 'Mean', lineStyle: {color: '#C0C4CC', width: 1}, endLabel: {color: '#945FB9', show: true, formatter: '{a}', distance: 1}, silent: true, type: 'line', smooth: true, symbol: 'none', zlevel: 2},
      {data: setting.sd2m, name: '-2SD', lineStyle: {color: '#945FB9', width: 1}, endLabel: {color: '#945FB9', show: true, formatter: '{a}', distance: 1}, silent: true, type: 'line', smooth: true, symbol: 'none', zlevel: 2},
      {data: setting.trueData || [], symbolSize: 6, silent: true, name: 'trueData', type: 'scatter', itemStyle: {opacity: 1}, zlevel: 3}
    ]
  };
  myChart.setOption(options);
  return myChart;
}