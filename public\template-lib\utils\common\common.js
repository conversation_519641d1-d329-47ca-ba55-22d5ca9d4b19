// var winTopParentStoreUserState = null;
// if(window.top.parentVueInstance && window.top.parentVueInstance.$store && 
//     window.top.parentVueInstance.$store.state && 
//     window.top.parentVueInstance.$store.state.user) {
//       winTopParentStoreUserState = window.top.parentVueInstance.$store.state.user;
// } else {
//   if(window === window.top && (location.href.indexOf('pypacs') > -1 || location.href.indexOf('3686') > -1)) {
//     winTopParentStoreUserState = JSON.parse(window.localStorage.getItem('webpacs_pathology') || '{}');
//   }
// }
// var userInfo = winTopParentStoreUserState && winTopParentStoreUserState.userInfo? winTopParentStoreUserState.userInfo : (JSON.parse(window.localStorage.getItem('general_userInfo') || '{}') || {});
// var token = winTopParentStoreUserState && winTopParentStoreUserState.token ? winTopParentStoreUserState.token : (JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || "");
var userInfo = JSON.parse(window.localStorage.getItem('general_userInfo') || '{}') || {};
var token = JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || '';
var storageEncryptKey = '0123456789ABCDEF';
var isDevFlag = location.href.indexOf('/sreport') === -1;   //true为开发模式
var localKey = getUrlParam('frontendParam');
//radio点击前的选中状态
var _is_checked_ = false;
if (localKey && isEncryptFun(localKey)) {
  localKey = decryptFun(localKey);
}
// AES加密方法以16进制输出
function encryptFun(message) {
  var key = CryptoJS.enc.Utf8.parse(storageEncryptKey)
  var srcs = CryptoJS.enc.Utf8.parse(message)
  var encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.ciphertext.toString().toUpperCase()
}

function decryptBase64(message) {
  var key = CryptoJS.enc.Utf8.parse(storageEncryptKey)
  var decrypt = CryptoJS.AES.decrypt(message, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}

// AES解密方法
function decryptFun(hexWord) {
  var wordArray = CryptoJS.enc.Hex.parse(hexWord)
  var base64Word = CryptoJS.enc.Base64.stringify(wordArray)
  return decryptBase64(base64Word)
}

function isEncryptFun(str) {
  if (!str) {
    return false;
  }
  if (str === encryptFun(decryptFun(str))) {
    return true;
  } else {
    return false;
  }
};

// 初始化数值输入框,带加减按钮
function initInpNumber(ele, callbackFun) {
  var inpNum = ele.find(".inp-number");
  inpNum.each(function (index, dom) {
    var inp = $(dom).find('.inp_inner');
    var min = inp.attr('min') || '';
    if (inp.val() === '') {
      inp.val(min);
    }
    setDisabled($(dom));
  })
  tiggerInpClick(ele, callbackFun);
}

// 绑定数值输入框的点击事件
function tiggerInpClick(ele, callbackFun) {
  ele.find(".inp-number").on("click", ".inp-number_decrease, .inp-number_increase", function (e) {
    var inpNum = $(this).parent().find(".inp_inner");
    var value = inpNum.val();
    if ($(this).hasClass('inp-number_decrease')) {
      inpNum.val(--value);
    }
    if ($(this).hasClass('inp-number_increase')) {
      inpNum.val(++value);
    }
    setDisabled($(this).parent());
    if (callbackFun && typeof callbackFun == 'function') {
      callbackFun(ele);
    }
  })
  ele.find(".inp-number").on("blur", ".inp_inner", function () {
    // var numReg = /\d/ig;
    var numReg = new RegExp($(this).attr("data-reg"), 'ig');
    var value = Number($(this).val());
    var min = $(this).attr('min');
    var max = $(this).attr('max');
    if (!numReg.test(value)) {
      $(this).val(min || '');
    } else {
      if (max !== undefined && value > Number(max)) {
        $(this).val(max);
      }
      if (min !== undefined && value < Number(min)) {
        $(this).val(min);
      }
    }
    setDisabled($(this).parent());
    if (callbackFun && typeof callbackFun == 'function') {
      callbackFun(ele);
    }
  })
}

// 判断是否超过临界值
function setDisabled(dom) {
  var inp = $(dom).find('.inp_inner');
  var min = inp.attr('min');
  var max = inp.attr('max');
  if (min !== undefined && inp.val() <= Number(min)) {
    $(dom).find(".inp-number_decrease").addClass("is-disabled");
  } else {
    $(dom).find(".inp-number_decrease").removeClass("is-disabled");
  }
  if (max !== undefined && inp.val() >= Number(max)) {
    $(dom).find(".inp-number_increase").addClass("is-disabled");
  } else {
    $(dom).find(".inp-number_increase").removeClass("is-disabled");
  }
}

// 纯数字输入类型
function inpNumberHandler(ele) {
  ele.find(".w-con").on("blur", ".only-inp-num", function () {
    // var numReg = /\d/ig;
    var numReg = new RegExp($(this).attr("data-reg"), 'ig');
    var inpVal = $(this).val().trim();
    if (inpVal === '') {
      return;
    }
    if (inpVal[inpVal.length - 1] === '.') {
      $(this).val(inpVal.replace('.', ''));
    }
    var value = Number($(this).val().trim());
    var min = $(this).attr('min');
    var max = $(this).attr('max');
    if (!numReg.test(value)) {
      $(this).val(min || '');
    } else {
      if (max !== undefined && value > Number(max)) {
        $(this).val(max);
      }
      if (min !== undefined && value < Number(min)) {
        $(this).val(min);
      }
    }
  })
}


// 当浅色块下的值没有选择任何一个的话，深色块隐藏
function toggleHightBlock(ele, firstIn) {
  var hightBlock = ele.find(".hight-block");
  ele.find(".hight-item").hide();
  hightBlock.hide();
  hightBlock.each(function (idx, block) {
    var lightId = $(block).attr("light-id");
    var lightW = ele.find('[id="' + lightId + '"]').find('.rt-sr-w');
    // 兼容新模版规则
    if (lightW.length === 0) {
      lightW = ele.find('[id="' + lightId + '"]');
    }
    if (lightW) {
      // 纯文本则判断下级的值是否为空
      if (lightW.hasClass('rt-sr-tit') || lightW.attr('rt-sc').indexOf('wt:;') > -1) {
        var childArr = nodesToArray(ele.find('[parent-id="' + lightId + '"]'));
        if (childArr.length === 0) {
          childArr = nodesToArray(ele.find('[pid="' + lightId + '"]'));
        }
        for (var i = 0; i < childArr.length; i++) {
          var child = childArr[i];
          if ($(child).attr('type') === 'radio') {
            if ($(child).is(':checked') && $(child).parents('.lb-active').length) {
              var childId = $(child).attr('id');
              $(block).find('[contect-id]').hide();
              $(block).find('[contect-id="' + childId + '"]').show();
              $(block).show();
              break;
            }
          } else {
            if ($(child).parents('.on').length || (firstIn && $(child).is(':checked'))) {
              var childId = $(child).attr('id');
              ele.find('[contect-id="' + childId + '"]').show();
              $(child).parents('.lb-row').addClass('on');
              $(block).show();
              break;
            }
          }
        }
      } else {
        if ((lightW.attr('type') === 'checkbox' && (lightW.parents('.on').length || (firstIn && $(lightW).is(':checked')))) ||
          (lightW.is(':checked') && lightW.attr('type') !== 'checkbox' && lightW.parents('.lb-active').length)) {
          var childId = lightW.attr('id');
          if (lightW.attr('type') === 'checkbox') {
            var groupName = lightW.attr('name');
            var showCheckboxLen = $('[name="' + groupName + '"]:checked').parents('.lb-row.on').length;
            if (firstIn) {
              if (showCheckboxLen === 0) {
                $(lightW).parents('.lb-row').addClass('on');
                ele.find('[contect-id="' + childId + '"]').show();
                $(block).show();
              }
            } else {
              $(lightW).parents('.lb-row').addClass('on');
              ele.find('[contect-id="' + childId + '"]').show();
              $(block).show();
            }
          } else {
            ele.find('[contect-id="' + childId + '"]').show();
            $(block).show();
          }
        }
      }
    }
  })
}

// 绑定浅色块中选择的应显示的深色块，并把其他值清空
function initLigthItemChange(ele) {
  ele.find(".lb-row", "input::after").on("click", function (e) {
    if ($(this).find('.rt-sr-w').attr('type') === 'checkbox') {
      e.stopPropagation();
      e.preventDefault();
      var checkbox = $(this).find('.rt-sr-w');
      if (!checkbox.is(":checked")) {
        checkbox.click();
      }
      $(this).addClass('on');
      $(this).siblings().removeClass('on');
      positionToLight(ele, checkbox);
    }
  })
  ele.find(".lb-row").on("click", ".rt-sr-w", function (e) {
    if ($(this).attr('type') === 'checkbox') {
      e.stopPropagation();
    }
  })
  ele.find(".lb-row").on("change", ".rt-sr-w", function (e) {
    if ($(this).attr('type') === 'checkbox') {
      $(this).parents('.lb-row').addClass('on');
      $(this).parents('.lb-row').siblings().removeClass('on');
    }
    positionToLight(ele, this);
  })
}

// 定位置某个对应的框
// 已经填写的项的值
function positionToLight(ele, that, hasFillItem) {
  var id = $(that).attr('id');
  // 清空其他表单的值
  var hightBlock = $(that).parents(".light-block").siblings(".hight-block");
  if (hightBlock.length === 0) {
    hightBlock = $(that).parents('.w-block').find(".hight-block");
  }
  if (ele.attr('type') === 'radio') {
    hightBlock.find('.rt-sr-w').each(function (index, wgt) {
      clearFormItemVal(wgt);
    })
  }
  // $(that).parents('.page').find(".hight-item").hide();
  ele.find(".hight-item").hide();
  var contectBlock = null;
  if ($(that).is(':checked')) {
    contectBlock = ele.find('[contect-id="' + id + '"]');
    contectBlock.show();
  }
  toggleHightBlock(ele);
  if (hasFillItem && contectBlock) {
    for (var key in hasFillItem) {
      var rtSrW = contectBlock.find('[data-wname="' + key + '"] .rt-sr-w');
      if (rtSrW.hasClass('rt-sr-r')) {
        contectBlock.find('[data-wname="' + key + '"] .rt-sr-w[value="' + hasFillItem[key] + '"]').prop('checked', true);
      } else if (rtSrW.hasClass('rt-sr-s')) {
        if (rtSrW.find('option[value="' + hasFillItem[key] + '"]').length === 0) {
          continue;
        }
        rtSrW.val(hasFillItem[key]);
      } else {
        rtSrW.val(hasFillItem[key]);
      }
    }
  }
}

// 清除表单的值
function clearFormItemVal(widget) {
  var type = getWgtType(widget) || widget.type;
  if (type === 'text') {
    $(widget).val('');
  } else if (['radio', 'checkbox'].indexOf(type) > -1) {
    $(widget).attr('checked', false);
  } else if (type === 'select') {
    $(widget).find('option').each(function (i, option) {
      // if(i === 0) {
      //   $(option).attr('selected', true);
      // } else {
      // }
      $(option).attr('selected', false);
    })
  } else {
    $(widget).val('');
  }
}

// 判断控件类型
function getWgtType(widget) {
  var widgetTypes = ['ck-checkbox', 'r-radio', 'tit-title', 't-text', 's-select'];
  for (var i = 0; i < widgetTypes.length; i++) {
    var name = widgetTypes[i].split('-')[0];
    var type = widgetTypes[i].split('-')[1];
    if ($(widget).hasClass('rt-sr-' + name)) {
      return type;
    }
  }
}

function nodesToArray(nodeList) {
  return Array.prototype.slice.call(nodeList);
}

// 消息提示框
function $wfMessage(option) {
  var defaultOption = {
    "type": "err",  //类型，err,success,warn
    "position": "top-center",   //位置，top-left,top-center,top-right,center-left,center-center,center-right,bottom-left,bottom-center,bottom-right
    "showicon": true,  //是否显示图标
    "content": "提示",
    "shadow": false,
    "shadowclickclose": true,
    "autoclose": true,
  }
  option = $.extend(defaultOption, option)
  WfMsg(option)
}

// 生成长度4的uuid
function createUUidFun() {
  return 'xxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// ajax请求
var ajaxUrlCon = [];
function fetchAjax(ajParams) {
  // type, url, params, contentType, successFn, errorFn
  var handler = function (params) {
    params.headers = params.headers || {};
    if (!params.headers.token) {
      params.headers.token = token || '';
    }
    $.ajax({
      type: params.type || 'post',
      url: params.url,
      data: params.data || {},
      dataType: params.dataType || "json",
      async: params.async !== undefined ? params.async : true,
      contentType: params.contentType || 'application/json',
      headers: params.headers,
      success: function (res) {
        if (res.status != '0' && !params.hideErrMsg) {
          $wfMessage({
            content: res.message || '操作失败'
          })
        }
        params.successFn && params.successFn(res);
      },
      error: function (jqXHR) {
        if (params.responseType === 'blob') {
          params.successFn && params.successFn(jqXHR.responseText);
        } else {
          $wfMessage({
            content: '网络故障，请稍后再试'
          })
          params.errorFn && params.errorFn(jqXHR);
        }
      },
      complete: function (jqXHR) {
        params.completeFn && params.completeFn(jqXHR);
        //请求完成清除当前url
        if (ajaxUrlCon && ajaxUrlCon.length > 1) {
          for (var i = 0; i < ajaxUrlCon.length; i++) {
            var item = ajaxUrlCon[i];
            if (item.url === params.url) {
              ajaxUrlCon.splice(i, 1);
              break;
            }
          }
          handler(ajaxUrlCon[0]);
        } else {
          ajaxUrlCon = [];
        }
      }
    });
  }
  if (ajParams.await) {
    ajaxUrlCon && ajaxUrlCon.push(ajParams);
    if (ajaxUrlCon && ajaxUrlCon.length === 1) {
      handler(ajaxUrlCon[0]);
    }
  } else {
    handler(ajParams);
  }
}

// 截取地址栏参数
function getUrlParam(name, url) {
  return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url || location.href) || [, ""])[1].replace(/\+/g, '%20')) || '';
}


// 获取或设置当前缓存的数据
function getOrSetCurReportLocal(type, data, key) {
  localKey = getUrlParam('frontendParam');
  if (localKey && isEncryptFun(localKey)) {
    localKey = decryptFun(localKey);
  }
  var queryName = 'sr_query_params';
  var localData = JSON.parse(window.localStorage.getItem(queryName) || '{}');
  if (type === 'get') {
    return localData[key || localKey] || {};
  }
  if (type === 'set') {
    localData[key || localKey] = data;
    window.localStorage.setItem(queryName, JSON.stringify(localData));
  }
}

// 从缓存中获取参数
function getParamByName(name) {
  var obj = getOrSetCurReportLocal('get');
  return obj[name] || '';
}

// 获取当前日期
function getCurDateAndTime(noSecond) {
  var date = new Date();
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var minute = date.getMinutes();
  var second = date.getSeconds();
  var curDate = year + '-' + addZero(month) + '-' + addZero(day)
  var curTime = addZero(hour) + ':' + addZero(minute) + (!noSecond ? ':' + addZero(second) : '');
  return { curDate: curDate, curTime: curTime };
}
//小于10的拼接上0字符串
function addZero(s) {
  return s < 10 ? ('0' + s) : s;
}

// 树转成一维数组
function treeToArr(data) {
  var arr = [];
  var handler = function (list) {
    list.forEach(function (item) {
      if (item.child && item.child.length) {
        var tempChild = JSON.parse(JSON.stringify(item.child));
        delete item.child;
        arr.push(item);
        handler(tempChild);
      } else {
        arr.push(item);
      }
    })
  }
  handler(data);
  return arr;
}

// 将页面相关表单生成结果json-->docAttr
function createResultData() {
  if (!rtStructure) {
    return {};
  }
  var resultData = rtStructure.exportStructureData();
  var arr = treeToArr(JSON.parse(JSON.stringify(resultData)));
  // 数组转以id为key的对象，方便使用
  var idMapResult = {};
  arr.forEach(function (item) {
    idMapResult[item.id] = item;
  });
  return {
    resultData: resultData,
    idMapResult: idMapResult
  }
}

// 实际保存的数据格式
function saveParams(data) {
  var resultData = data.resultData;
  var patternInfo = data.patternInfo;
  var patientInfo = data.patientInfo;
  var params = {
    patDocId: patternInfo.patDocId,
    patternName: patternInfo.patternName,
    patternId: patternInfo.patternId,
    optType: data.optType || '0',  //操作类型0保存 1提交 2审核 3复审
    optId: data.optId,
    optName: data.optName,
    docId: data.docId || '',   //新增/更新的标识
    docNo: data.docNo || '',
    mainFlag: data.mainFlag || '',   //是否为主模板的标识
    docContent: {
      description: data.description || '',
      impression: data.impression || '',
      recommendation: data.recommendation || '',
      busInfo: {
        busId: data.busId,
        busType: data.busType,
        name: patientInfo.name,
        sex: patientInfo.sex,
        age: patientInfo.age,
        birthDate: patientInfo.birthDate,
      },
      docAttr: resultData
    }
  }
  return params;
}


// 动态添加idAndDomMap值
function addIdAndDomMap(id, option) {
  var defaultOption = {
    id: id,
    pvf: '',
    req: '',
    rtScPageNo: 1,
    wt: '',
    vt: '',
    value: ''
  }
  var resObj = $.extend(defaultOption, option);
  rtStructure.idAndDomMap[id] = resObj;
}

// 添加加载遮罩
function addLoadCoverLayer() {
  if ($(".sr-load-cover").length) {
    return;
  }
  var src = '';
  if (location.href.indexOf('/sreport') > -1) {
    src = '/sreport';
  }
  var layer = '<div class="sr-load-cover" style="position:fixed;top:0;left:0;right:0;bottom:0;background:#000;opacity:0.6;filter: Alpha(opacity=60)">';
  layer += '<img style="position: fixed;left: 50%;top: 50%;" src="' + src + '/template-lib/layouts/assets/images/loading.png">';
  layer += '</div>';
  $('body').append($(layer));
}
// 移除遮罩
function removeLoadCoverLayer() {
  $(".sr-load-cover").remove();
}

// 保存操作
function saveHandler(params, cb) {
  var successCb = cb ? cb.successCb : null;
  var errorCb = cb ? cb.errorCb : null;
  addLoadCoverLayer()
  fetchAjax({
    url: params.docId ? api.updateSrResult : api.saveSrResult,
    data: JSON.stringify([params]),
    successFn: function (res) {
      if (res.status == '0') {
        $wfMessage({
          type: 'success',
          content: res.message || '操作成功',
        })
        if (successCb && typeof successCb === 'function') {
          successCb(res);
        }
      } else {
        if (errorCb && typeof errorCb === 'function') {
          errorCb();
        }
      }
    },
    errorFn: function () {
      if (errorCb && typeof errorCb === 'function') {
        errorCb();
      }
    },
    completeFn: function () {
      removeLoadCoverLayer();
    }
  })
}

// 获取已保存的结果数据json
function getResJsonHandler(params, cb) {
  var successCb = cb ? cb.successCb : null;
  var errorCb = cb ? cb.errorCb : null;
  fetchAjax({
    url: api.getContentList,
    data: JSON.stringify(params),
    successFn: function (res) {
      if (res.status == '0') {
        if (successCb && typeof successCb === 'function') {
          successCb(res);
        }
      } else {
        if (errorCb && typeof errorCb === 'function') {
          errorCb();
        }
      }
    },
    errorFn: function () {
      if (errorCb && typeof errorCb === 'function') {
        errorCb();
      }
    }
  })
}

// 获取模板详细内容html
function getParDocHtmlHandler(params, cb) {
  var successCb = cb ? cb.successCb : null;
  var errorCb = cb ? cb.errorCb : null;
  if (!params.patDocId) {
    $wfMessage({
      content: '该类型尚未配置相关模板'
    })
    removeLoadCoverLayer();
    return;
  }
  fetchAjax({
    url: api.patternInfo,
    data: JSON.stringify(params),
    successFn: function (res) {
      if (res.status == '0') {
        if (successCb && typeof successCb === 'function') {
          successCb(res);
        }
      } else {
        if (errorCb && typeof errorCb === 'function') {
          errorCb();
        }
      }
    },
    errorFn: function () {
      if (errorCb && typeof errorCb === 'function') {
        errorCb();
      }
    },
    completeFn: function () {
      removeLoadCoverLayer();
    }
  })
}

// 获取模板列表
function getPatternListHandler(params, cb) {
  var successCb = cb ? cb.successCb : null;
  var errorCb = cb ? cb.errorCb : null;

  fetchAjax({
    url: api.patternList,
    data: JSON.stringify(params),
    successFn: function (res) {
      if (res.status == '0') {
        if (successCb && typeof successCb === 'function') {
          successCb(res);
        }
      } else {
        if (errorCb && typeof errorCb === 'function') {
          errorCb();
        }
      }
    },
    errorFn: function () {
      if (errorCb && typeof errorCb === 'function') {
        errorCb();
      }
    },
  })
}

// 判断是否为移动端
function isMobilePlatform() {
  if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
    return true;
  }
  return false;
}

// 获取科室人员列表
function getDepartUserList(staffType) {
  var params = {
    hospitalCode: userInfo.hospitalCode || '',
    deptCode: userInfo.deptCode || '',
    staffType: staffType,
  }
  var departUserList = [];
  fetchAjax({
    url: api.getPeopleList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var list = res.result || [];
        list.forEach(function (item, index) {
          item.value = item.name;
          item.id = item.staffNo || item.userId;
        })
        departUserList = list;
      }
    },
  })
  return departUserList;
}

function keydown_to_tab(pageId) {
  curElem.on('keydown', 'input:text:not([n-enterjump="1"]):not(:disabled):not(.rt-hide)', function (e) {
    var svId = $(this).attr('id');
    // 找出可见且可编辑的输入框
    var inpList = pageId ? $(`#${pageId} input:text:not([n-enterjump="1"]):not(:disabled):not(.rt-hide):visible`) : [];
    if (!inpList.length) inpList = $('input:text:not([n-enterjump="1"]):not(:disabled):not(.rt-hide):visible');
    var n = inpList.length;
    if (e.which == 13 || e.which == 40) {
      e.preventDefault();
      var nextIndex = inpList.index(this) + 1;
      // 特殊处理：sv（66）——肺动脉（48）——右心房（45,46）跳转顺序
      if(svId === 'to-rt-66') {
        // sv
        $('#to-rt-66').blur();
        $('#to-rt-48').focus();
      }else if(svId === 'to-rt-48') {
        // 肺动脉
        $('#to-rt-48').blur();
        $('#to-rt-45').focus();
      }else if(svId === 'to-rt-45') {
        // 右心房1
        $('#to-rt-45').blur();
        $('#to-rt-46').focus();
      }else if(svId === 'to-rt-46') {
        // 右心房2
        $('#to-rt-46').blur();
        let toInpIsDis = $('#to-rt-68').attr('disabled');
        if(toInpIsDis) {
          // FS
          $('#to-rt-70').focus();
        }else {
          // CO
          $('#to-rt-68').focus();
        }
      }
      // 特殊处理：左心室（舒张末）——左心室后壁——左心室（收缩末）
      else if(svId === 'to-rt-37') {
        // 左心室（舒张末）
        $('#to-rt-37').blur();
        $('#to-rt-41').focus();
      }else if(svId === 'to-rt-41') {
        // 左心室后壁
        $('#to-rt-41').blur();
        $('#to-rt-39').focus();
      }else if(svId === 'to-rt-39') {
        // 左心室（收缩末）
        $('#to-rt-39').blur();
        $('#to-rt-62').focus();
      }
      else {
        // 正常从上往下跳转
        if (nextIndex < n) {
          inpList[nextIndex].focus();
        } else {
          inpList[nextIndex - 1].blur();
          nextIndex = 0;
          inpList[nextIndex].focus();
        }
      }
    }
    if (e.which == 38) {
      e.preventDefault();
      var nextIndex = inpList.index(this) - 1;
      // 特殊处理逆向：右心房2（46）——右心房1（45）——肺动脉（48）——sv（66）
      if(svId === 'to-rt-70') {
        let toInpIsDis = $('#to-rt-68').attr('disabled');
        if(toInpIsDis) {
          // FS
          $('#to-rt-70').blur();
          // 右心房2
          $('#to-rt-46').focus();
        }else {
          $('#to-rt-70').blur();
          // CO
          $('#to-rt-68').focus();
        }
      }else if(svId === 'to-rt-68') {
        // 右心房2
        $('#to-rt-68').blur();
        $('#to-rt-46').focus();
      }else if(svId === 'to-rt-46') {
        // 右心房1
        $('#to-rt-46').blur();
        $('#to-rt-45').focus();
      }else if(svId === 'to-rt-45') {
        // 肺动脉
        $('#to-rt-45').blur();
        $('#to-rt-48').focus();
      }else if(svId === 'to-rt-48') {
        // sv
        $('#to-rt-48').blur();
        $('#to-rt-66').focus();
        // 上一个
      }else if(svId === 'to-rt-66') {
        // EF
        $('#to-rt-66').blur();
        $('#to-rt-62').focus();
      }
      
      // 特殊处理逆向：左心室（收缩末）——左心室后壁——左心室（舒张末）
      else if(svId === 'to-rt-62') {
        // 左心室（收缩末）
        // 失去焦点上一个
        $('#to-rt-62').blur();
        $('#to-rt-39').focus();
      }else if(svId === 'to-rt-39') {
        // 左心室后壁
        $('#to-rt-39').blur();
        $('#to-rt-41').focus();
      }else if(svId === 'to-rt-41') {
        // 左心室（舒张末）
        $('#to-rt-41').blur();
        $('#to-rt-37').focus();
      }
      
      else {
        // 逆向，从下往上跳转
        if (nextIndex >= 0) {
          inpList[nextIndex].focus();
        } else {
          nextIndex = 0;
          inpList[nextIndex].blur();
          nextIndex = n - 1;
          inpList[nextIndex].focus();
        }
      }
    }
  })
}

// 点击单选按钮取消选中
function toggleRadioCheckStatus() {
  curElem.find("input[type=radio]:not(.uncancel)").off('mousedown').off('click');
  curElem.find("input[type=radio]:not(.uncancel)").parent().off('click');
  curElem.find("input[type=radio]:not(.uncancel)").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
      rtStructure.setChildrenDisabled(this);
      iput.trigger('change');
    }
  });
  curElem.find("input[type=radio]:not(.uncancel)").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

// 获取元素的值
function getVal(selector, joinChar, parentElem) {
  parentElem = parentElem || curElem;
  var dom = parentElem.find(selector);
  var valArr = [];
  if (dom.length) {
    dom.each(function (i, el) {
      if ($(el).val() || $(el).text()) {
        valArr.push($(el).val() || $(el).text());
      }
    })
  }
  return valArr.length ? valArr.join(joinChar || '、') : '';
}

/**
 * 根据出生日期推算年龄，单位（日，周，月，岁）
 * @param {string} birthdayStr 出生日期
 * @param {string} separator  年龄结果值数值与单位之间的分隔符，默认''
 * @return {string} 带单位的年龄,错误值为'-1'
 */
function inferAgeByBirthday(birthdayStr, separator) {
  if (!birthdayStr) {
    return '-1';
  }
  // 出生年份  出生月份  出生日
  let [birthYear, birthMonth, birthDay] = birthdayStr.split("-").map(Number);
  separator = separator ? separator : '';
  let d = new Date();
  let nowYear = d.getFullYear();		//当前年
  let nowMonth = d.getMonth() + 1;	//当前月
  let nowDay = d.getDate();			//当前日

  let ageY = nowYear - birthYear;		//岁
  let ageM;							//月
  let ageT;							//日
  // 月份对应的天数
  let MonthDayMap = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  if (birthDay > nowDay) {
    nowMonth = nowMonth - 1;
    if (nowMonth === 0) {
      ageY = ageY - 1;
      nowMonth = 12;
      nowDay += MonthDayMap[nowMonth - 1];
    } else {
      if (nowMonth === 2) {
        if (nowYear % 4 === 0 && nowYear % 100 !== 0 || nowYear % 400 === 0) {
          nowDay = nowDay + 28;
        } else {
          nowDay = nowDay + 29;
        }
      } else {
        nowDay += MonthDayMap[nowMonth - 1];
      }
    }
    ageT = nowDay - birthDay;
  } else {
    ageT = nowDay - birthDay;
  }
  if (birthMonth > nowMonth) {
    ageY = ageY - 1;
    nowMonth = nowMonth + 12;
    ageM = nowMonth - birthMonth;
  } else {
    ageM = nowMonth - birthMonth;
  }
  let age = '';
  if (ageY < 0) {
    age = '-1';
  } else if (ageY === 0) {
    if (ageM === 0) {
      if (ageT > 0 && ageT % 7 === 0) {
        age = ageT / 7 + separator + '周';
      } else {
        age = ageT + separator + '日';
      }
    } else {
      age = ageM + separator + '月';
    }
  } else if (ageY < 4) {
    age = ageY + separator + '岁' + separator + ageM + separator + '月';
  } else {
    age = ageY + separator + '岁';
  }
  return age;
}

/**
 * 根据年龄推算出生日期
 * @param {string} ageYear 岁
 * @param {string} ageMonth 月
 * @param {string} ageDay 日
 * @param {string} separator  出生日期结果连接符，默认'-'
 * @return {string} 以separator连接的日期
 */
function inferBirthdayByAge(ageYear, ageMonth, ageDay, separator) {
  let subYear = isNaN(parseInt(ageYear)) ? 0 : parseInt(ageYear);
  let subMonth = isNaN(parseInt(ageMonth)) ? 0 : parseInt(ageMonth);
  let subDay = isNaN(parseInt(ageDay)) ? 0 : parseInt(ageDay);
  let now = new Date();
  let nowYear = now.getFullYear();
  let nowMonth = now.getMonth() + 1;
  let nowDay = now.getDate();
  // 先day相减，然后month相减，最后year相减
  let day = nowDay - subDay;
  let month = nowMonth - subMonth;
  let year = nowYear - subYear;
  separator = separator ? separator : '-';
  // day小于等于0则获得上月的天数，依次递减
  const computeDay = function (nowMonth, nowYear) {
    let lastMonth = nowMonth - 1;
    let lastMonthOfYear = nowYear;
    if (lastMonth <= 0) {
      lastMonth = lastMonth + 12;
      lastMonthOfYear = lastMonthOfYear - 1;
    }
    day = day + new Date(lastMonthOfYear, lastMonth, 0).getDate();
    console.log(day, new Date(lastMonthOfYear, lastMonth, 0).getDate());
    month = month - 1;
    if (day <= 0) {
      computeDay(lastMonth, lastMonthOfYear);
    }
  }
  if (day <= 0) {
    computeDay(nowMonth, nowYear);
  }

  const computeMonth = function () {
    month = month + 12;
    year--;
    if (month <= 0) {
      computeMonth();
    }
  }
  if (month <= 0) {
    computeMonth();
  }
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  let birthday = year + separator + month + separator + day;
  return birthday;
}

// 获取用户签名图像接口
function getSignImgHandler(params, isPng) {
  var imgUrlBase64 = '';
  fetchAjax({
    url: api.getSignImage,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.result && res.result.base64Content) {
        var prefix = isPng ? 'data:image/png;base64,' : 'data:image/jpg;base64,';
        var resBase = prefix + res.result.base64Content;
        // var blob = dataURLtoFile(resBase, '签名图像.png', 'blob');
        // imgUrlBase64 = window.URL.createObjectURL(blob);
        imgUrlBase64 = resBase;
      }
    },
  })
  return imgUrlBase64;
}

// base64转文件
function dataURLtoFile(dataurl, filename, fileType) {
  let arr = dataurl.split(",");
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  if (fileType === 'blob') {
    //转换成成blob对象
    return new Blob([u8arr], { type: mime });
  } else {
    return new File([u8arr], filename, { type: mime });
  }
}

//获取结构化模板标本类型
function getSampleTypeList(examClass, examSubClass) {
  var sampleTypeList = [];
  var params = {};
  examClass ? params.examClass = examClass : '';
  examSubClass ? params.examSubClass = examSubClass : '';
  fetchAjax({
    url: api.getSampleTypeList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        sampleTypeList = res.result || [];
      }
    },
  })
  return sampleTypeList;
}

/**
 * 获取文档属性
 * @param {string} examNo 检查流水号
 * @param {string} patternId 模板编号
 * @param {Array} localCode 本地码
 */
function getDocAttrs(examNo, patternId, localCode) {
  var array = Object.prototype.toString.call(localCode).slice(8, -1)
  if (array !== 'Array') {
    return null
  }
  var attrsMap = {}
  fetchAjax({
    url: api.getDocAttr,
    data: JSON.stringify({
      examNo: examNo,
      patternId: patternId,
      localCode: localCode.join(',')
    }),
    async: false,
    successFn: function (res) {
      if (res.status === '0') {
        var result = res.result || []
        for (var i = 0; i < result.length; i++) {
          attrsMap[result[i].localCode] = result[i].attrValue
        }
      }
    }
  })
  return attrsMap
}
/**
 * 获取报告项
 * getExamRptList
 * @param {string} examNo 检查流水号
 * @param {string} rptType 报告类型 0常规 1冰冻 2免疫 3补充
 * @param {string} sortAsc 1按时间倒序
*/
function getExamRptList(examNo, rptType, sortAsc) {
  if (!examNo || !rptType) {
    return null
  }
  var rpt = []
  fetchAjax({
    url: api.getExamRptList,
    data: JSON.stringify({
      examNo: examNo,
      rptType: rptType,
      sortAsc: sortAsc !== undefined ? sortAsc : '1'
    }),
    async: false,
    successFn: function (res) {
      if (res.status === '0') {
        rpt = res.result || []
      }
    }
  })
  return rpt;
}

/**
 * 获取冰冻检查信息
 * getExamRptList
 * @param {string} examNo 检查流水号
*/
function getFreezeRegisterInfo(examNo) {
  if (!examNo) {
    return null;
  }
  var freezeRptInfo = {}
  fetchAjax({
    url: api.freezeRegisterInfo,
    data: JSON.stringify({
      examNo: examNo,
    }),
    async: false,
    successFn: function (res) {
      if (res.status === '0') {
        var freezeRptList = res.result || {};
        var gmActList = freezeRptList.gmActList || [];
        // actType 1：标本采集 2：标本送出 3：标本接收 4：取材 5：制片 6：诊断
        if(gmActList.length > 0) {
          for(var i = 0; i < gmActList.length; i++) {
            var gmAct = gmActList[i];
            if(gmAct.actEndDate) {
              var dateAndTime = gmAct.actEndDate + ' ' + gmAct.actEndTime;
              if(!freezeRptInfo['date_'+ gmAct.actType]) {
                freezeRptInfo['date_'+ gmAct.actType] = [dateAndTime];
              } else {
                freezeRptInfo['date_'+ gmAct.actType].push(dateAndTime);
              }
            }
            if(gmAct.actor) {
              if(!freezeRptInfo['doctor_'+ gmAct.actType]) {
                freezeRptInfo['doctor_'+ gmAct.actType] = [gmAct.actor];
              } else {
                freezeRptInfo['doctor_'+ gmAct.actType].push(gmAct.actor);
              }
            }
          }
        }
        freezeRptInfo['freezeRptList'] = freezeRptList;
      }
    }
  })
  return freezeRptInfo;
}

//获取蜡块明细
function getExamCandleList(examNo, source, ascColumn) {
  var waxBlockList = [];
  var params = {};
  examNo ? params.examNo = examNo : '';
  source ? params.source = source : '';
  ascColumn ? params.ascColumn = ascColumn : '';
  fetchAjax({
    url: api.getExamCandleList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        waxBlockList = res.result || [];
      }
    },
  })
  return waxBlockList;
}

// 获取蜡块所有取材、带教医生并去重, joinChar多人时连接符:默认空格
function commonGetDoctorListByCandle(examNo, source, ascColumn, joinChar) { 
  var candleList = getExamCandleList(examNo, source, ascColumn);
  if(!candleList.length) {
    return {};
  }
  var totalDoctors = {
    cutUser: [],  // 取材医生
    leadCutUser: [],  // 取材带教
    register: [],  // 记录员
    leadRegister: [],  // 记录带教
  };
  candleList.forEach(function(candle) { 
    for(var key in totalDoctors) {
      if(candle[key] && totalDoctors[key].indexOf(candle[key]) === -1) {
        totalDoctors[key].push(candle[key]);
      }
    }
  })
  return {
    cutUser: totalDoctors['cutUser'].length > 0 ? totalDoctors['cutUser'].join(joinChar || ' ') : '',
    leadCutUser: totalDoctors['leadCutUser'].length > 0 ? totalDoctors['leadCutUser'].join(joinChar || ' ') : '',
    register: totalDoctors['register'].length > 0 ? totalDoctors['register'].join(joinChar || ' ') : '',
    leadRegister: totalDoctors['leadRegister'].length > 0 ? totalDoctors['leadRegister'].join(joinChar || ' ') : '',
  };
}

//获取切片列表
function commonGetGlassList(examNo) {
  if(!examNo) {
    return [];
  }
  var glassList = [];
  var params = {
    examNo: examNo
  };
  fetchAjax({
    url: api.getGlassInfoForSR,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        glassList = res.result || [];
      }
    },
  })
  return glassList;
}

// 获取切片所有制片、特染医生并去重, joinChar多人时连接符:默认空格
function commonGetDoctorListByGlass(examNo, joinChar) { 
  var glassList = commonGetGlassList(examNo);
  if(!glassList.length) {
    return '';
  }
  var totalDoctors = {
    heUser: [],  // HE医生
    zhUser: [],  // 免疫组化医生
    trUser: [],  // 特殊染色医生
  };
  glassList.forEach(function(glass) { 
    var glassUser = glass.glassUser;
    if(!glass.specialFlag) {
      // 无该标识的都是HE
      if(totalDoctors['heUser'].indexOf(glassUser) === -1) {
        totalDoctors['heUser'].push(glassUser);
      }
    } else {
      if(glass.orderType === '免疫组化') {
        if(totalDoctors['zhUser'].indexOf(glassUser) === -1) {
          totalDoctors['zhUser'].push(glassUser);
        }
      } else if(glass.orderType === '特殊染色') {
        if(totalDoctors['trUser'].indexOf(glassUser) === -1) {
          totalDoctors['trUser'].push(glassUser);
        }
      }
    }
  })
  var totalDoctorStrArr  = [];
  totalDoctors['heUser'].length > 0 && totalDoctorStrArr.push('HE-' + totalDoctors['heUser'].join(joinChar || ' '))
  totalDoctors['zhUser'].length > 0 && totalDoctorStrArr.push('组化-' + totalDoctors['zhUser'].join(joinChar || ' '))
  totalDoctors['trUser'].length > 0 && totalDoctorStrArr.push('特染-' + totalDoctors['trUser'].join(joinChar || ' '))
  return totalDoctorStrArr.length ? totalDoctorStrArr.join('；') : '';
}

//获取当前检查的标本部位
function commonGetSampleOrganList(params) {
  var sampleOrganList = [];
  fetchAjax({
    url: api.gmSampleNameList,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        sampleOrganList = res.result || [];
      }
    },
  })
  return sampleOrganList;
}

//获取科室报告医生列表
function getReporterList(params) {
  var reporterList = [];
  fetchAjax({
    url: api.getReporterList,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        reporterList = res.result || [];
      }
    },
  })
  return reporterList;
}

// 将大体所见中的#到换行符的内容清除(目前只有省医需要调用)
function replaceSeenConByJh(text) {
  if(!text) {
    return '';
  }
  // 不含#，原文返回
  if(text.indexOf('#') === -1) {
    return text;
  }
  var splitArr = text.split('\n');
  var replacedArr = [];
  for(var i = 0; i < splitArr.length; i++) {
    if(!splitArr[i]) {
      replacedArr.push(splitArr[i]);
      continue;
    }
    // 不含#，整行去掉
    if(splitArr[i].indexOf('#') === -1) {
      continue;
    }
    var strSplitByJH = splitArr[i].split('#')[0] || '';
    // var strSplitByQcText = strSplitByJH.split('取材/记录')[0] || '';
    if(strSplitByJH) {
      replacedArr.push(strSplitByJH);
    }
  }
  var replacedStr = replacedArr.join('\n');
  return replacedStr;
}

/**
 * @description 重置单选框值
 * @param {String} pageId  页面id
 * @param {String} className 类名
 * @param {String} key  键值
 * @param {Object} data 数据详情
 */
function resetEleVal(pageId,className,key,data) {
  var eleList = $('#'+pageId+' .'+className).find('input[type="radio"]');
  eleList.each(function(i,dom) {
    var ele = $(dom);
    var value = ele.val();
    ele.removeClass('rt-checked');
    ele.prop('checked',false);
    if((value === '是' && data[key]==='1') || (value === '否' && data[key]==='0')) {
      ele.prop('checked',true);
      ele.addClass('rt-checked');
    }
  })
}

// 设置缓存localstorage数据
function setLocalStorage(key, val) {
  var valStr = JSON.stringify(val);
  window.localStorage.setItem(key, valStr);
}
// 获取缓存localstorage数据
function getLocalStorage(key) {
  if(!window.localStorage.getItem(key)) {
    return;
  }
  return JSON.parse(window.localStorage.getItem(key));
}
// 移除缓存localstorage数据
function removeLocalStorage(key) {
  window.localStorage.removeItem(key, valStr);
}

// 存在住院号显示住院号，否则为门诊号
// 传入label类名，值的选择器,publicInfo报告信息
function replaceViewPatternByValue(labelSelector, valueSelector, publicInfo) {
  var outpatientNo = publicInfo.outpatientNo;
  var inpatientNo = publicInfo.inpatientNo;
  var showInpat = publicInfo.patientSource === '住院' || inpatientNo;  //显示为住院号
  var label = $(labelSelector).text();
  label = label.replace(/门诊号|住院号/, showInpat ? '住院号' : '门诊号');
  $(labelSelector).text(label);
  $(valueSelector).text(showInpat ? inpatientNo : outpatientNo);
}

//获取模板节点的默认值
function getDefaultByPatternId(patternId) {
  var defaultValueMap = {}, defaultValueList = [];  //默认值集合
  var params = {
    patternId: patternId || '',
  };
  fetchAjax({
    url: api.getSrDict,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        // 过滤出name=sr_dict_开头的属性，reagentNo试剂号
        var res = res.result || [];
        for(var i = 0; i < res.length; i++) {
          var item = res[i];
          if(item.name.indexOf('sr_dict_') > -1) {
            var key = item.name.split('sr_dict_')[1] || '';
            if(key) {
              defaultValueMap[key] = item.value;
              defaultValueList.push({...defaultValueMap,...item});
            }
          }
        }
      }
    },
  })
  return defaultValueList;
}

/**
 * @description 过滤模板节点默认的对应键值
 * @param {Array} data 模板节点的默认值
 * @param {String} filteKey 键值名称
*/
function filterKeyMap(data,filteKey) {
  let valueMap = {};
  for(var i = 0; i < data.length; i++) {
    var item = data[i];
    var key = item.name.split('sr_dict_')[1] || '';
    if(key.indexOf(filteKey) > -1) {
      if(key) {
        valueMap[key] = item.value;
      }
    }
  }
  return valueMap;
}


/**
 * @description 检测文本是否超出容器，超出后进行缩放
 * @param {Object} container 容器节点
 * @param {Object} textDom 文本节点，table不要传入，lodop不兼容
 */
function checkOverflow(container, textDom) {
  if(!container) {
    return;
  }
  document.body.style.zoom = 1 / window.devicePixelRatio;  //先还原页面和浏览器比例
  var contentH = 0;  //内容的高度
  var containerH = container.offsetHeight;  //容器的可视高度
  var gapH = 0;  //table增加间隙高度，为lodop做兼容
  // 如果没有指定子节点，则以其内容进行判断
  if(!textDom) {
    var hiddenDom = container.cloneNode(true);
    hiddenDom.style.opacity = '0';
    hiddenDom.style.position = 'absolute';
    hiddenDom.style.overflow = 'auto';
    hiddenDom.style.height = 'auto';
    hiddenDom.style.maxHeight = 'unset';
    hiddenDom.style.minHeight = 'unset';
    hiddenDom.style.bottom = 'unset';
    hiddenDom.style.width = container.offsetWidth + 'px';
    container.parentElement.appendChild(hiddenDom);
    contentH = hiddenDom.offsetHeight;
    if($(hiddenDom).find('table').length) {
      gapH = 50;
    }
    hiddenDom.remove();
  } else {
    contentH = textDom.offsetHeight;
    if(textDom.nodeName === 'TABLE') {
      gapH = 50;
    }
  }
  if (contentH > containerH) {
    // 文本超出容器高度，需要进行缩放
    var scaleFactor = containerH / (contentH + gapH);
    var newDomEle = textDom;
    if(!newDomEle) {
      newDomEle = document.createElement('div');
      newDomEle.innerHTML = container.innerHTML;
      container.innerHTML = '';
      container.appendChild(newDomEle);
    }
    // transform使用lodop不生效
    // newDomEle.style.transform = `scaleY(${scaleFactor})`;
    // newDomEle.style.transformOrigin = `top left`;
    var newDomW = newDomEle.offsetWidth;
    // 不断尝试缩放，知道接近1结束
    for(var scale = scaleFactor; scale < 1; scale += 0.05) {
      var lastZoom = newDomEle.style.zoom || scaleFactor;  //上一次的比例
      // 原比例缩放，宽度保持不变
      newDomEle.style.zoom = `${scale}`;
      newDomEle.style.width = (newDomW / scale) + 'px';
      var textZoomH = newDomEle.offsetHeight + (gapH * scale);
      var truthTextH = textZoomH * scale;   //实际的缩放高度
      if(truthTextH > containerH) {
        newDomEle.style.zoom = `${lastZoom}`;
        newDomEle.style.width = (newDomW / lastZoom) + 'px';
        break;
      }
    }
    $(container).parents('.t-pg').css('overflow', 'hidden');
  }
}

//获取用户列表
function getDictUsersBaseList(params) {
  var usersBaseList = [];
  fetchAjax({
    url: api.getDictUsersBaseList,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        usersBaseList = res.result || [];
      }
    },
  })
  return usersBaseList;
}

//获取护理护士列表
function getNurseList(params,staffType,type) {
  var nurseList = [];
  var deptCode = window.localStorage.getItem('userInfo_drug_deptCode') || '';
  params.deptCode = deptCode || '';
  params.staffType = type === 'doctor' ? '' : '2';
  fetchAjax({
    url: api.getNurseList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        nurseList = res.result || [];
      }
    },
  })
  return nurseList;
}

//获取护理系统参数
function getSystemParamInfo(params) {
  var systemList = [];
  fetchAjax({
    url: api.getSystemParamInfo,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        systemList = res.result || [];
      }
    },
  })
  return systemList;
}

//获取护理字典列表
function getCommonUseList(params) {
  var dictionaryList = [];
  fetchAjax({
    url: api.getCommonUseList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        dictionaryList = res.result || [];
      }
    },
  })
  return dictionaryList;
}
//获取护理服务器时间
function getDateTime() {
  var nowDateTime = '';
  let params = {}
  fetchAjax({
    url: api.getDateTime,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0'&&res.result) {
        nowDateTime = res.result.nowDateTime;
      }
    },
  })
  return nowDateTime;
}

// 病理pacs，获取相关医生列表
/**
 * 
 * @param {object} params 
 * params = {
 *  deptCode: 当前科室code
 *  staffType: 医生类型0:诊断医生 1:技师 2:护士 3:其它 4:患者 5:手术医生 6:实验技师 多选逗号分隔
 * }
 * @returns 医生列表
 */
function getAllUserList(params) {
  var userList = [];
  fetchAjax({
    url: api.getAllUserList,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        userList = res.result || [];
      }
    },
  })
  return userList;
}

//获取检查显影剂信息
function getExamImageAgentList(params) {
  var agentList = [];
  fetchAjax({
    url: api.getExamImageAgentList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        agentList = res.result || [];
      }
    },
  })
  return agentList;
}

//获取护理检查详情信息
function getExamPatientInfoList(params) {
  var agentList = {};
  fetchAjax({
    url: api.getExamPatientInfoList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        agentList = res.result || {};
      }
    },
  })
  return agentList;
}

// 获取病理AI数据
function getPacsAiData(params, callBack) {
  var result = {};
  fetchAjax({
    url: api.getRptAiDiag,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        result = res.result;
        if(callBack && typeof callBack === 'function') {
          callBack(result);
        }
      }
    },
  })
  return result;
}

/**
 * (同步)上传图片并保存图片id到模板内的图片id集合节点
 * @param {String} examNo 检查号
 * @param {File} file 图片文件
 * @param {HTMLElement} rtEl 图片id集合节点元素，写在模板内的隐藏节点，<input type="text" style="display:none" class="rt-sr-w" sr-name="上传的图像id集合">
 * @returns {String} 图片id
 */
function imageUploader(examNo, file, rtEl) {
  if (!examNo) {
    $wfMessage({content: 'examNo不能为空'});
    return;
  }
  if (!file || !(file instanceof File)) {
    $wfMessage({content: 'file不能为空'});
    return;
  }
  if (!rtEl) {
    $wfMessage({content: 'rtEl不能为空'});
    return;
  }
  var formData = new FormData();
  var _pid = new Date().getTime() + '.jpg';
  var curPid;
  formData.append('examNo', examNo);
  formData.append('file', file);
  formData.append('fileName', _pid);
  formData.append('imageType', 'rpt');
  $.ajax({
    type: 'post',
    url: api.uploadReportFileNew,
    data: formData,
    async: false,
    contentType: false,
    processData: false,
    headers: {token: token || ''},
    success: function (res) {
      if (res.status == '0') {
        curPid = _pid;
        var valArr = getImageIdList(rtEl);
        valArr.push(curPid);
        $(rtEl).val(valArr.join(','));
      } else {
        $wfMessage({
          content: res.message
        });
      }
    },
    error: function () {
      $wfMessage({content: '网络故障，请稍后再试'});
    }
  });
  return curPid;
}

/**
 * (异步)根据图片id集合节点获取图片地址列表
 * @param {String} examNo 检查号
 * @param {HTMLElement} rtEl 图片id集合节点元素
 * @param {Function} callBack 回调函数，参数是图片地址列表
 */
function getUploadedImageList(examNo, rtEl, callBack) {
  if (!examNo) {
    $wfMessage({content: 'examNo不能为空'});
    return;
  }
  if (!rtEl) {
    $wfMessage({content: 'rtEl不能为空'});
    return;
  }
  if (typeof callBack !== 'function') {
    $wfMessage({content: 'callBack不能为空'});
    return;
  }
  var valArr = getImageIdList(rtEl);
  if (valArr.length === 0) {
    callBack([]);
  }
  var urls = [], loadLens = [];
  valArr.map(function(item, i) {
    getUploadedImageUri(examNo, item, i, function(uri, imgIndex) {
      // 保持顺序
      if(uri) {
        urls[imgIndex] = uri
      }
      // uri && urls.push(uri);
      loadLens.push(imgIndex)
      if (loadLens.length === valArr.length) {
        callBack(urls);
      }
    });
  });
  return urls;
}

/**
 * (异步)根据图片id获取图片地址
 * @param {String} examNo 检查号
 * @param {String} pid 图片id
 * @param {Function} callBack 回调函数，参数是图片地址，图片无效时是空字符串
 */
function getUploadedImageUri(examNo, pid, imgIndex, callBack) {
  if (!examNo) {
    $wfMessage({content: 'examNo不能为空'});
    return;
  }
  if (!pid) {
    $wfMessage({content: 'pid不能为空'});
    return;
  }
  if (typeof callBack !== 'function') {
    $wfMessage({content: 'callBack不能为空'});
    return;
  }
  var url = api.getRptImg + '?examNo=' + examNo + '&fileName=' + pid + '&t=' + new Date().getTime();
  fetch(url, {
    headers: {token: token || ''},
  }).then(res => {
    return res.blob();
  }).then(blb => {
    if (blb.size === 0) {
      callBack('', imgIndex);
    } else {
      callBack(URL.createObjectURL(blb), imgIndex);
    }
  });
}

/**
 * (同步)删除ftp上的图片文件，并从图片id集合节点删除图片id
 * @param {String} examNo 检查号
 * @param {String} pid 要删除的图片id
 * @param {HTMLElement} rtEl 图片id集合节点元素
 * @returns {Boolean} 是否删除成功
 */
function deleteUploadedImage(examNo, pid, rtEl) {
  var isSuccess;
  fetchAjax({
    url: api.deleteByImageType,
    data: JSON.stringify({examNo, fileName: pid, imageType: 'rpt'}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        isSuccess = true;
      }
    },
  });
  if (!isSuccess) {
    return false;
  }
  var valArr = getImageIdList(rtEl);
  var newValArr = valArr.filter(function(item) {
    return item !== pid;
  });
  $(rtEl).val(newValArr.join(','));
  return true;
}

/**
 * 从图片id集合节点获取图片id列表
 * @param {HTMLElement} rtEl 图片id集合节点元素
 * @returns {Array<String>} 图片id列表
 */
function getImageIdList(rtEl) {
  var val = $(rtEl).val();
  if (!val) {
    return [];
  }
  var valArr = val.split(',');
  return valArr;
}

/**
 * 渲染下拉类型表单，内部已支持点击选项后填写内容到输入框，可以传入layui.dropdown.render支持的参数
 * @param {Object} options 参数
 * @param {String} options.selector 输入框选择器
 * @param {Array<Object>|Array<String>} options.dataList 下拉数据列表
 * @param {String} [options.titleName] 显示在下拉菜单的内容的键名。如果options.dataList为Array<String>，可忽略
 * @param {String} [options.valueName] 选择后填到输入框的值的键名。如果options.dataList为Array<String>，可忽略
 * @param {Array<String>|Boolean} [options.filterKeys] 手动输入时搜索的键名列表，传入这参数即开启选项搜索功能
 * @example
 *
  // 普通使用
  var dataList1 = ['选项1', '选项2', '选项3'];
  renderDropdown({
    selector: '.class1',
    dataList: dataList1
  });
  // 开启选项过滤
  renderDropdown({
    selector: '.class2',
    dataList: dataList1,
    filterKeys: true
  });

  var dataList2 = [{name: '选项1', code: 'xx1'}, {name: '选项2', code: 'xx2'}, {name: '选项3', code: 'xx3'}];
  renderDropdown({
    selector: '.class3',
    dataList: dataList2,
    titleName: 'name'
  });
  // 开启选项过滤
  renderDropdown({
    selector: '.class4',
    dataList: dataList2,
    titleName: 'name',
    filterKeys: ['name', 'code']
  });
 */
function renderDropdown(options) {
  var selector = options.selector;
  var dataList = options.dataList;
  var titleName = options.titleName;
  var valueName = options.valueName;
  var filterKeys = options.filterKeys;
  var customClick = options.click;
  delete options.click;
  if (!selector || typeof selector !== 'string') {
    console.error('无效selector');
    return;
  }
  if ($(selector).length === 0) {
    console.error('无效selector');
    return;
  }
  if (!dataList || !(dataList instanceof Array) || dataList.length === 0) {
    console.error('无效dataList');
    return;
  }
  if (typeof dataList[0] === 'string') {
    titleName = '';
    valueName = '';
  }
  var dropOpts;
  if (titleName) {
    dropOpts = transformDropdownOptList(dataList, titleName);
  } else {
    dropOpts = transformDropdownOptList(dataList);
  }
  var isFilter = (filterKeys instanceof Array && filterKeys.length > 0 && typeof filterKeys[0] === 'string') || (typeof filterKeys === 'boolean' && filterKeys);
  var showKey = valueName || 'title';
  var renderOptions = {
    elem: selector,
    data: dropOpts,
    className: 'laySelLab',
    click: function(data, othis) {
      this.elem.val(data[showKey]);
      this.elem.focus();
      if (isFilter) {
        layui.dropdown.reload(this.id, {
          data: dropOpts,
          show: false
        });
      }
      if (customClick) {
        return customClick.call(this, data, othis);
      }
    }
  };
  for (var k in options) {
    renderOptions[k] = options[k];
  }
  if (!isFilter) {
    layui.dropdown.render(renderOptions);
    return;
  }
  $(selector).each(function() {
    if (!this.id) {
      this.id = ('d' + Math.random()).replace('.', '');
    }
    renderOptions.elem = '#' + this.id;
    renderOptions.id = this.id;
    layui.dropdown.render(renderOptions);
    var TT;
    // 监听输入，根据输入内容过滤选项
    $(this).on('input', function() {
      var el = this;
      clearTimeout(TT);
      TT = setTimeout(function() {
        var val = el.value.trim();
        var newOpts;
        if (!val) {
          newOpts = dropOpts;
        } else {
          newOpts = dropOpts.filter(function(item) {
            if (typeof filterKeys === 'boolean') {
              if (item.title.indexOf(val) !== -1) {
                return item;
              }
            } else {
              var isTarget = filterKeys.some(function(k) {
                return item[k] && item[k].indexOf(val) !== -1;
              });
              if (isTarget) {
                return item;
              }
            }
          });
        }
        layui.dropdown.reload(el.id, {
          data: newOpts,
          show: true
        });
      }, 200);
    });
  });
}

/**
 * 转换下拉数据格式
 * @param {Array<Object>|Array<String>} dataList 下拉数据列表
 * @param {String} [titleName] 显示在下拉菜单的内容的键名
 * @returns {Array}
 */
function transformDropdownOptList(dataList, titleName) {
  var arr = [];
  dataList.forEach((item, index) => {
    if(titleName) {
      var obj = {
        title: item[titleName],
        id: index,
        templet: '<span title="' + item[titleName] + '">' + item[titleName] + '</span>'
      };
      for (let k in item) {
        obj[k] = item[k];
      }
      arr.push(obj);
    }else {
      arr.push({
        title: item,
        id: index,
        templet: '<span title="' + item + '">' + item + '</span>'
      });
    }
  })
  return arr;
}
 /**
  * @description 点击日期选择框，自动选中当前光标所在位置的日期
  * @param {String} elementId 日期选择框的id
  * @param {Object} options 配置选项
 */
function setupDateSelection(elementId, options) {
  const element = document.getElementById(elementId);
  // 设置默认配置选项
  const defaultOptions = {
    year: true,
    month: true,
    day: true,
    hour: true,
    minute: true,
    second: true
  };

  // 合并默认配置选项和传入的配置选项
  const config = Object.assign({}, defaultOptions, options);
  if(!element){
    return
  }
   // 定义各个部分的范围
   const yearRange = [0, 4];
   const monthRange = [5, 7];
   const dayRange = [8, 10];
   const hourRange = [11, 13];
   const minuteRange = [14, 16];
   const secondRange = [17, 19];
  element.addEventListener('click', function(event) {
    const dateValue = element.value;

    // 获取光标位置
    const cursorPosition = getCursorPosition(element);

   

    // 根据配置选项决定是否选中各部分
    const shouldSelectYear = config.year;
    const shouldSelectMonth = config.month;
    const shouldSelectDay = config.day;
    const shouldSelectHour = config.hour;
    const shouldSelectMinute = config.minute;
    const shouldSelectSecond = config.second;

    // 根据光标位置选中相应部分
    if (shouldSelectYear && cursorPosition >= yearRange[0] && cursorPosition <= yearRange[1]) {
      selectText(element, yearRange[0], yearRange[1] );
    } else if (shouldSelectMonth && cursorPosition >= monthRange[0] && cursorPosition <= monthRange[1]) {
      selectText(element, monthRange[0], monthRange[1] );
    } else if (shouldSelectDay && cursorPosition >= dayRange[0] && cursorPosition <= dayRange[1]) {
      selectText(element, dayRange[0], dayRange[1] );
    } else if (shouldSelectHour && cursorPosition >= hourRange[0] && cursorPosition <= hourRange[1]) {
      selectText(element, hourRange[0], hourRange[1] );
    } else if (shouldSelectMinute && cursorPosition >= minuteRange[0] && cursorPosition <= minuteRange[1]) {
      selectText(element, minuteRange[0], minuteRange[1] );
    } else if (shouldSelectSecond && cursorPosition >= secondRange[0] && cursorPosition <= secondRange[1]) {
      selectText(element, secondRange[0], secondRange[1] );
    }
  });
  element.addEventListener('input',function(event){
    const cursorPosition = getCursorPosition(element);
    if(cursorPosition===yearRange[1]){
      selectText(element,monthRange[0],monthRange[1])
    }else if(cursorPosition===monthRange[1]){
      selectText(element,dayRange[0],dayRange[1])
    }else if(cursorPosition===dayRange[1]){
      selectText(element,hourRange[0],hourRange[1])
    }else if(cursorPosition===hourRange[1]){
      selectText(element,minuteRange[0],minuteRange[1])
    }else if(cursorPosition===minuteRange[1]){
      selectText(element,secondRange[0],secondRange[1])
    }
  })
}

// 获取光标位置的函数
function getCursorPosition(element) {
  if (document.selection) {  // IE
    var sel = document.selection.createRange();
    sel.moveStart('character', -element.value.length);
    return sel.text.length;
  } else if (element.selectionStart || element.selectionStart === '0') {  // Firefox, Chrome, Opera
    return element.selectionStart;
  }
  //  else {  // Safari
  //   var range = element.createTextRange();
  //   range.collapse(true);
  //   return range.moveEnd('character', -element.value.length) + range.moveEnd('character', element.value.length);
  // }
}

// 选中文本的函数
function selectText(element, start, end) {
  if (element.setSelectionRange) {
    element.setSelectionRange(start, end);
  } else if (element.createTextRange) {
    var range = element.createTextRange();
    range.collapse(true);
    range.moveStart('character', start);
    range.moveEnd('character', end - start);
    range.select();
  }
}
// 数字补全，兼容IE8
function padNumberWithZeros(number, totalLength) {
  var numStr = number.toString();
  while (numStr.length < totalLength) {
    numStr = '0' + numStr;
  }
  return numStr;
}
//获取护理造影剂
function getAgent(params) {
  var dictionaryList = [];
  fetchAjax({
    url: api.getAgent,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        dictionaryList = res.result || [];
      }
    },
  })
  return dictionaryList;
}

function useServerTime(id){
  if(window.getDateTime){
    var dateTime = window.getDateTime();
    $('#'+id).val(dateTime);
  }
}
function bindTimeFocus(id,date) {
  $('#'+id).on('focus', function(value) {
    if($('#'+id).val()){
      return
    }
    date? $('#'+id).val(getCurDateAndTime().curDate) : $('#'+id).val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
}
// 添加按钮到指定位置
function addLayDateTime(id,dateType='datetime') {
  if(rtStructure&&rtStructure.enterOptions.type==='view'){
    return
  }
  let $element = $('#' + id);
  let $parent = $element.parent();
  $parent.css({
    display: 'flex',
    'align-items': 'center'
  });

  let $btn = $('<span>')
    .html('<i class="layui-icon layui-icon-date"></i>')
    .css({
      'margin-left': '4px',
      cursor: 'pointer'
    })
    .attr('id', id + '-time')
    .insertAfter($element);

  $btn.on('click', function() {
    layui.use('laydate', function() {
      var laydate = layui.laydate;
      laydate.render({
        elem: '#' + id + '-time', // 指定元素
        type: dateType,
        show: true,
        done: function(value, date, endDate) {
          $btn.html('<i class="layui-icon layui-icon-date"></i>');
          $element.val(value);
        }
      });
    });
  });
}

// 文本域随内容增高而增高,dom传入textarea元素
function autoAdjustTextareaHeight(dom, initFlag) {
  $(dom).css('height', 'auto');  //恢复高度用于计算
  var adjustHandler = function(target) {
    var writeByEditorFlag = $(target).attr('h-resize') === '1';  //h-resize区分编辑器的模板
    var minHeight = parseInt($(target).css('minHeight'));
    var newHeight = target.scrollHeight;
    var resetDom = target;
    // 编辑器模板的处理
    if(writeByEditorFlag) {
      resetDom = $(target).parents('.w-con');
      minHeight = resetDom.attr('minH') ? Number(resetDom.attr('minH')) : resetDom.outerHeight();
      resetDom.attr('minH', minHeight);
      // 父容器加上内边距和边框
      newHeight += parseInt(resetDom.css('padding-top') || '0') + parseInt(resetDom.css('padding-bottom') || '0')
        + parseInt(resetDom.css('border-top-width') || '0') + parseInt(resetDom.css('border-bottom-width') || '0');
    }
    if(!isNaN(minHeight)) {
      if(minHeight < newHeight) {
        $(resetDom).css('height', newHeight + 'px');
      } else {
        $(resetDom).css('height', minHeight + 'px');
      }
    } else {
      $(resetDom).css('height', newHeight + 'px');
    }
    // 编辑器的处理
    if(writeByEditorFlag) {
      $(target).css('height', '100%');
      // 处理完高度调整其下方的元素位置，避免重叠
      adjustLowerBroElemTop(resetDom, minHeight);
    }
  }
  // 初始化高度加点延时
  if(initFlag) {
    setTimeout(function() {
      adjustHandler(dom);
    }, 0)
  } else {
    adjustHandler(dom)
  }
}

/**
 * 编辑器：编辑下调整高度后调整其他元素位置
 * @param {*} dom 参照的元素
 * @param {*} minH 参照的元素的最小高度即原设计高度
 */
var domDiffObj = {};  //记住上一个元素的差值
function adjustLowerBroElemTop(dom, minH) {
  // 业务上的使用
  var absoluteElePosition = [];
  if($('[isview]').length > 0) {
    absoluteElePosition = rtStructure.absoluteElePosition;
  } else {  //编辑器内的预览
    absoluteElePosition = window.absoluteElePosition;
  }
  function adjustTopHandler(referDom, minHeight, isLayout) {
    var referDomId = isLayout ? $(referDom).attr('id') : $(referDom).find('.rt-sr-w').attr('id');
    var domHeight = $(referDom).outerHeight();
    var handler = function(newDiff) {
      var diff = newDiff !== undefined ? newDiff : (domHeight - minHeight);  //增加的高度
      var domLyId = $(referDom).closest('.rt-layout').attr('id');
      var domIndex = -1;
      var domTop = 0;
      domDiffObj[referDomId] = diff;
      // 调整兄弟元素的top
      absoluteElePosition.forEach(function(item, i) {
        if(!isLayout) {
          if(item.id === domLyId) {
            var children = item.children || [];
            children.forEach(function(child, cIdx) {
              // 找到与参照元素之后的兄弟元素，且兄弟元素位置+高度>参照元素高度
              if(domIndex !== -1 && child.top !== '' && 
              (minHeight + domTop < child.top + $('#' + child.id).closest('.w-con').outerHeight())) {
                var diffSum = getDiffTopSum(children, cIdx, domDiffObj);
                $('#' + child.id).closest('.w-con').css('top',(Number(child.top) + diffSum) + 'px');
              }
              if(domIndex === -1 && child.id === referDomId) {
                child.diff = diff;
                domIndex = cIdx;
                domTop = Number(child.top);
              }
            })
            // 调整父容器的top
            var layoutDom = $(referDom).closest('.rt-layout');
            layoutDom.css('height', commonAdjustParentHeight(layoutDom[0], item.height) + 'px');
            adjustTopHandler(layoutDom[0], item.height, true)
            return;
          }
        } else {
          // 找到与参照元素之后的兄弟元素
          if(domIndex !== -1 && item.top !== '' && 
            (minHeight + domTop < item.top + $('#' + item.id).outerHeight())) {
            var diffSum = getDiffTopSum(absoluteElePosition, i, domDiffObj);
            $('#' + item.id).css('top',(Number(item.top) + diffSum) + 'px');
          }
          if(domIndex === -1 && item.id === referDomId) {
            domIndex = i;
            domTop = Number(item.top);
          }
        }
      })
    }
    // 未发生变化无需调整
    if(domHeight <= minHeight) {
      delete domDiffObj[referDomId];
      handler(0);
      return;
    }
    handler();
  }
  adjustTopHandler(dom, minH);
  // 调整画布的高度
  var pageDom = $('[is-rt-editor="1"] .page');
  pageDom.css('height', commonAdjustParentHeight(pageDom[0], pageDom.outerHeight()) + 'px');
}

// 获取当前元素以上的元素已调整的top差值合, index当前要调整的项
function getDiffTopSum(children, index, domDiffObj) {
  var curChild = children[index];
  var sum = 0;
  children.forEach(function(child, i) {
    // 当前元素以上的元素已调整的top差值合，top小于参照元素的高度+top忽略不叠加
    if(index > i && curChild.top > child.height + child.top) {
      sum += domDiffObj[child.id] || 0;
    }
  })
  return sum;
}

// 编辑器：含定位子元素进行调整父元素的高度
function commonAdjustParentHeight(parentElement, originalHeight) {
  var children = Array.from(parentElement.children); // 获取所有子元素
  if (!children?.length) {
    return originalHeight;
  }
  var totalHeight = 0;  //所有子元素高度之和
  var notAbsElementHeight = 0;  // 非定位元素高度之和
  var bottomHeight = 0;  // 底部元素高度
  for (var child of children) {
    var style = window.getComputedStyle(child);
    // 找出非定位的元素大小
    if (style.position !== 'absolute' && style.position !== 'fixed') {
      notAbsElementHeight += child.offsetHeight;
    } else {
      // 找出定位元素的最大底部位置
      var top = child.offsetTop;
      var height = child.offsetHeight;
      // 设置了bottom的元素
      if(child.style.bottom) {
        bottomHeight += $(child).outerHeight(true);
      }
      totalHeight = Math.max(totalHeight, top + height);
    }
  }
  totalHeight = Math.max(totalHeight, notAbsElementHeight) + bottomHeight + 2;
  return totalHeight;
}

// layui下拉框支持键盘上下键选择，li-active为选中的项,keydownAliveFlag避免重复事件
if(!document.keydownAliveFlag) {
  document.keydownAliveFlag = true;
  document.addEventListener('keydown', function(e) {
    var dropdownElem = $('.layui-dropdown-menu:visible');
    if(!dropdownElem.length) {
      return;
    }
    var dropdownItems = dropdownElem.find('> li');
    var selectedIndex = dropdownElem.find('> li.li-active').index(); // 当前选中的项索引
    var keyName = e.key ? e.key.toLowerCase() : '';
    if(keyName === 'arrowdown') { // 向下键
      e.preventDefault();
      if(selectedIndex < dropdownItems.length - 1) {
        selectedIndex++;
        bindSelectKey(dropdownElem, dropdownItems, selectedIndex)
      }
    } else if(keyName === 'arrowup') { // 向上键
      e.preventDefault();
      if(selectedIndex > 0) {
        selectedIndex--;
        bindSelectKey(dropdownElem, dropdownItems, selectedIndex)
      }
    } else if(keyName === 'enter') { // 回车键
      e.preventDefault();
      if(selectedIndex >= 0) {
        // 执行选择操作
        dropdownItems.eq(selectedIndex).click();
      }
      dropdownItems.removeClass('li-active');
    }
  })
  
  // 选中上下键修改的样式
  function bindSelectKey(dropdownElem, dropdownItems, selectedIndex) {
    dropdownItems.removeClass('li-active');
    dropdownItems.eq(selectedIndex).addClass('li-active');
    var top = dropdownItems.eq(selectedIndex).position().top;
    dropdownElem.parent('.layui-dropdown')[0].scrollTop = top;
  }
}

// 获取结构化配置
function getSrSettings(params){
  // params = {
  //   "paramName":"", // 参数名称，例如该需求为 sr_dict_measure_param_group
  //   "reserve1":"" // 模板代码 例如该需求为 2145
  // };
  var settings = [];
  fetchAjax({
    url: api.getSrSettings,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        settings = res.result || [];
      }
    },
  })
  return settings;
}
//获取测量参数值
function getMeasureParamList(params){
  var measureParamList = []
  fetchAjax({
    url: api.getMeasureParamList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        measureParamList = res.result || [];
      }
    },
  })
  return measureParamList;
}

// 获取当前检查的生长曲线数据
function getExamMeasureParamList(params) {
  var list = [];
  fetchAjax({
    url: api.getExamMeasureParamList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        list = res.result || [];
      }
    },
  });
  return list;
}

// 上传文件,params  json
function reportFilePatch(params) {
  var formData = new FormData();
  for(var key in params) {
    formData.append(key, params[key]);
  }
  var fileRes = {};
  $.ajax({
    type: 'post',
    url: api.filePatch2,
    data: formData,
    async: false,
    contentType: false,
    processData: false,
    headers: {token: token || ''},
    success: function (res) {
      if (res.status == '0') {
        fileRes = res.result || {};
      } else {
        $wfMessage({
          content: res.message
        });
      }
    },
    error: function () {
      $wfMessage({content: '网络故障，请稍后再试'});
    }
  });
  return fileRes;
}

// 根据fileNo获取文件路径
function getFileUrlByFileNo(params, callBack) {
  fetch(api.getFileUrlByFileNo, {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      token: token || '',
      'Content-Type': 'application/json'
    },
  }).then(res => {
    if (!res.ok) {
      throw new Error('请求失败');
    }
    return res.blob();
  }).then(blb => {
    if(blb.size > 0) {
      const blob = new Blob([blb], { type: 'application/pdf' });
      callBack(blb.size === 0 ? '' : blob);
      // const url = window.URL.createObjectURL(blob);
      // window.open(url);
    } else {
      callBack('');
    }
  })
}

//获取测量参数字典
function getParamCodeList(){
  var list = [];
  fetchAjax({
    url: api.getParamCodeList,
    data: '{}',
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        list = res.result || [];
      }
    },
  })
  return list;
}

/**
 * 病理-获取标本列表
 * @param {String} actType 获取标本指定类型的数据，0：离体，1：固定，2：接收，3：退回，4：脱钙，6：脱脂，8：送切，A:入库，D:销毁 C:采集时间
 * @returns 指定类型的标本数据，未指定actType返回所有标本数据
 */
function getSampleInfoForSR(examNo, actType){
  if(!examNo) {
    return [];
  }
  var list = [];
  fetchAjax({
    url: api.getSampleInfoForSR,
    data: JSON.stringify({examNo: examNo}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        let data = res.result || [];
        if(actType) {
          data.forEach(function(item){
            var actList = item.actList || [];
            delete item.actList;
            if(actList.length) {
              actList.forEach(function(act) {
                if(act.actType === actType) {
                  if(act.actBeginDate) {
                    act.actBeginTimeStamp = new Date(act.actBeginDate + ' ' + act.actBeginTime).getTime();
                  }
                  if(act.actEndDate) {
                    act.actEndTimeStamp = new Date(act.actEndDate + ' ' + act.actEndTime).getTime();
                  }
                  list.push($.extend(item, act));
                }
              })
              // 按开始时间升序
              list = list.sort(function(a, b) {
                return a.actBeginTimeStamp - b.actBeginTimeStamp;
              })
            }
          })
        } else {
          list = data;
        }
      }
    },
  })
  return list;
}

// 批量修改省医logo样式，后续不要用
function resetLogoStyle() {
  $('img[src*="/images/srm/logo"]').css({
    'width': '426px',
    'height': '60px',
    'marginLeft': '-20px',
  })
}

// 获取云诊断二维码，二维码为动态元素插入到指定容器
function getCloudFilmQrCodeUrl(examNo, container, tipText) {
  var params = {examNo: examNo, imageWidth: 200, imageHeight: 200};
  fetchAjax({
    url: api.getCloudFilmQrCodeUrl,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0' && res.result) {
        var qrCodeUrl = 'data:image/png;base64,' + res.result;
        var qrCodeHtml = '<div id="yunQrcode" style="position:absolute;top: 0;left:0;text-align:center">';
        qrCodeHtml += '<div class="qr-code-box">';
        qrCodeHtml += '<img src="'+qrCodeUrl+'" style="width: 70px;height: 70px;" />';
        qrCodeHtml += '</div>';
        qrCodeHtml += '<div style="margin-top: 5px;font-size:14px;color:#303133;font-weight:bold">'+(tipText || '扫码查看报告')+'</div>';
        qrCodeHtml += '</div>';
        if(container[0]) {
          container.append($(qrCodeHtml));
          if(window.getComputedStyle(container[0]).position === 'static') {
            container.css({
              'position': 'relative',
            })
          }
          // var qrcode = new QRCode(document.querySelector('#yunQrcode .qr-code-box'), {
          //   text: '',
          //   width: 70,
          //   height: 70,
          //   colorDark: '#303133',
          //   colorLight: '#ffffff',
          //   correctLevel: QRCode.CorrectLevel.H
          // });
          // qrcode.makeCode(qrCodeUrl);
        }
      }
    }
  })
}


// 按检查流水号获取操作人员姓名，appendDom是将插入的dom元素
function getOperatorName(publicInfo, appendDom) {
  var operatorName = {};
  var params = {
    examNo: publicInfo.examNo,
    rptType: publicInfo.rptType || (publicInfo.examRpt && publicInfo.examRpt.flags ? publicInfo.examRpt.flags[4] : '0')
  };
  fetchAjax({
    url: api.getOperatorUserByExamNo,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0' && res.result) {
        // embedUser 包埋技术员  数据来源:  exam_gm_candle_act.actor 
        // cutUser 取材医生  数据来源:  exam_gm_candle.cut_user
        // cutRegisterUser 取材记录员   数据来源:  exam_gm_candle.register
        // glassUser 切片技术员  数据来源 : exam_gm_glass.glass_user
        operatorName = res.result || {};
        if(appendDom) {
          var html = '';
          var operatorTypeList = [
            {key: 'cutUser', name: '取材医生'},
            {key: 'cutRegisterUser', name: '取材记录员'},
            {key: 'embedUser', name: '包埋技术员'},
            {key: 'glassUser', name: '切片技术员'},
          ];
          for(var i = 0; i < operatorTypeList.length; i++) {
            if(operatorName[operatorTypeList[i].key]) {
              html += '<div style="width:25%;display:flex">' + operatorTypeList[i].name + '：<span style="flex:1;text-align:left">'+ operatorName[operatorTypeList[i].key] +'</span></div>';
            }
          }
          if(html) {
            var newDomHtml = '<div style="display:flex;border-bottom: 1px solid #999;padding:14px 0;font-size:14px;color:#303133">'+html+'</div>';
            $(appendDom).after($(newDomHtml));
          }
        }
      }
    },
  })
  return operatorName;
}

// 界面滚动时关闭layui-dropdown下拉菜单
window.addEventListener('message', function(res) {
  var {message, data} = res.data || {};
  if(message !== 'srHtmlLoaded') {
    return;
  }
  $('#singleDisDefaultPage,.t-pg,.page').on('scroll', function() {
    if ($('.layui-dropdown:visible').length === 0) {
      return;
    }
    $(document).trigger('mousedown');
  });
});

// 预览页面动态添加id到绑定的节点上，用于样式工具栏操作。req17332
function addIdToNodeByView(node, keyList, wtIdAndDomMap) {
  try {
    if(typeof keyList === 'string') {
      keyList = [keyList];
    }
    let rtId = keyList.find(k => k.indexOf('rt-') > -1);
    var id = node.id || '';
    if(node.id) {
      $(node).attr('ctx-id', node.id);
    }
    if(!node.id && rtId) {
      $(node).attr('ctx-id', rtId);
      id = rtId;
    }
    // 将样式插入到id节点上
    if(id && wtIdAndDomMap && wtIdAndDomMap[id] && wtIdAndDomMap[id].htmlValue) {
      node.style.cssText += `;${wtIdAndDomMap[id].htmlValue}`;
    }
  } catch (err) {
    console.error(err)
  }
  
}

// 病理pascs提取相关结果--手写模板、编辑器模板
// widgetParams 更多参数
function getPacsPickResult(examNo, pickType, appendDom, widgetParams) {
  var apiMap = {
    'pickSample': api.gmSampleForRpt,   //提标本
    'pickSeen': api.getSampleSeen,     //提肉眼所见
    'pickMedical': api.getGmOrderForRpt,  //提医嘱结果
    'pickFrozen': api.getFrozenForRpt,   //提冰冻诊断
  }
  var originVal = appendDom.val();
  var wrapText = '';
  if (originVal && originVal[originVal.length - 1] !== '\n') {
    wrapText = '\n';
  }
  var params = {
    examNo: examNo
  }
  if (pickType === 'pickFrozen') {
    params.rptType = '1';
  }
  if (pickType === 'pickMedical' && widgetParams) {
    params.orderTypeFlag = widgetParams.orderTypeFlag;
  }
  fetchAjax({
    url: apiMap[pickType],
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        if (res.result) {
          var handler = function(resText) {
            var text = originVal + wrapText + resText;
            appendDom.val(text);
            appendDom.focus();
          }
          var resText = pickType !== 'pickSeen' ? res.result : (res.result.examRecord || '');
          handler(resText);
        }
      }
    },
  })
}

// 获取电子签名数据，publicInfo公共的检查/报告信息
function getSignDataHandler(publicInfo, id) { 
  // 非审核70/复审80状态不触发
  if(publicInfo.reportStatus !== '70' && publicInfo.reportStatus !== '80') {
    $('#' + id).remove();
    return;
  }
  var params = {
    examNo: publicInfo.examNo,
    reportNo: publicInfo.reportNo
  };
  var data = '';
  fetchAjax({
    url: api.getRptSignData,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0' && res.result) {
        data = res.result;
      }
    },
  })
  if(!data) {
    $('#' + id).remove();
  }
  return data || '';
}

/**
 * 对电子签名区域缩放，确保在容器内放下
 * @param {*} id 整个容器id 
 * @param {*} labelClass 标签class   
 * @param {*} wrapClass 实际显示签名数据的class   
 */
function scaleSignArea(id, labelClass, wrapClass) { 
  setTimeout(function() {
    var wrap = $(id).find(wrapClass);
    if(!wrap.text()) {
      return;
    }
    wrap.css('word-break', 'break-word');
    var lbHeight = $(id).find(labelClass).outerHeight(true) || 0;
    var idHeight = $(id).outerHeight();
    var oriHeight = idHeight - lbHeight;   //内容可用高度
    var curHeight = wrap.outerHeight() || 0;  //内容实际高度
    if(curHeight > oriHeight) {
      var scaleFactor = oriHeight / curHeight;
      var newDomEle = wrap[0];
      var newDomW = newDomEle.offsetWidth;
      // 不断尝试缩放，知道接近1结束
      for(var scale = scaleFactor; scale < 1; scale += 0.05) {
        var lastZoom = newDomEle.style.zoom || scaleFactor;  //上一次的比例
        // 原比例缩放，宽度保持不变
        newDomEle.style.zoom = `${scale}`;
        newDomEle.style.width = (newDomW / scale) + 'px';
        var textZoomH = newDomEle.offsetHeight + scale;
        var truthTextH = textZoomH * scale;   //实际的缩放高度
        if(truthTextH > oriHeight) {
          newDomEle.style.zoom = `${lastZoom}`;
          newDomEle.style.width = (newDomW / lastZoom) + 'px';
          break;
        }
      }
    }
  }, 0);
}