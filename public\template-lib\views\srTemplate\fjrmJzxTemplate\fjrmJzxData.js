// 评分推算等级
// 根据得分自动推出对应分级：
// C-TIRADS2类（-1分）
// C-TIRADS3类（0分）
// C-TIRADS类4a（1分）
// C-TIRADS4b类（2分）
// C-TIRADS4c类（3-4分）
// C-TIRADS5类（5分）
var scoreAndLevelMap = {
  '-1': {level: '2', color: '#40C71A', advice: ''},
  '0': {level: '3', color: '#40C71A', advice: '建议定期复查'},
  '1': {level: '4A', color: '#F3F055', advice: '建议定期复查/FNA'},
  '2': {level: '4B', color: '#F5222D', advice: '建议定期复查/FNA'},
  '3': {level: '4C', color: '#F5222D', advice: '建议定期复查/FNA'},
  '4': {level: '4C', color: '#F5222D', advice: '建议定期复查/FNA'},
  '5': {level: '5', color: '#F5222D', advice: '建议FNA'},
}
var levelAndColorMap = {
  '2': '#40C71A',
  '3': '#40C71A',
  '4A': '#F3F055',
  '4B': '#F5222D',
  '4C': '#F5222D',
  '5': '#F5222D',
  '6': '#F5222D',
}
// 病灶图
// 键值拼接顺序,拼音缩写命名
var jzxBzJoinKey = ['zc','zy','zd','sx','sc','r','l','bfqc', 'dyqc', 'qqc','cqqc','xb-qr','lqr','rzc','rzd','rsx','rqr','lzc','lzd','lsx'];
var jzxBzImageObj = {
  'zc': 'jzx-normal.png',   //正常
  'zzy': 'jzx-1.png', //有锥叶

  'zd': 'jzx-2.png', //肿大-双侧
  'zd-sc': 'jzx-2.png', //肿大-双侧
  'zd-r': 'jzx-3.png', //肿大-右侧
  'zd-l': 'jzx-4.png', //肿大-左侧

  'sx': 'jzx-5.png', //缩小-双侧
  'sx-sc': 'jzx-5.png', //缩小-双侧
  'sx-r': 'jzx-6.png', //缩小-右侧
  'sx-l': 'jzx-7.png', //缩小-左侧

  'r-bfqc': 'jzx-8.png', //右侧-部分切除
  'r-dyqc': 'jzx-27.png', //右侧-单叶切除
  'r-qqc': 'jzx-9.png', //右侧-全切除
  'r-cqqc': 'jzx-10.png', //右侧-次全切除

  'l-bfqc': 'jzx-11.png', //左侧-部分切除
  'l-dyqc': 'jzx-24.png', //右侧-单叶切除
  'l-qqc': 'jzx-12.png', //左侧-全切除
  'l-cqqc': 'jzx-13.png', //左侧-次全切除

  'sc-bfqc': 'jzx-14.png', //双侧-部分切除
  'sc-cqqc': 'jzx-15.png', //双侧-次全切除
  'sc-qqc': 'jzx-16.png', //双侧-全切除

  'xb-qr': 'jzx-17.png', //峡部缺如
  'zc-xb-qr': 'jzx-17.png', //大小正常，峡部缺如
  'zd-xb-qr': 'jzx-18.png', //肿大-双侧-峡部缺如
  'zd-sc-xb-qr': 'jzx-18.png', //肿大-双侧-峡部缺如
  'zd-r-xb-qr': 'jzx-19.png', //肿大-右侧-峡部缺如
  'zd-l-xb-qr': 'jzx-20.png', //肿大-左侧-峡部缺如
  'xb-qr-rqr': 'jzx-9.png', //峡部缺如（右侧缺如）
  'xb-qr-rqr-lzc': 'jzx-9.png', //峡部缺如（右侧缺如，左正常）
  'xb-qr-rqr-lzd': 'jzx-9.png', //峡部缺如（右侧缺如，左增大）
  'xb-qr-rqr-lsx': 'jzx-9.png', //峡部缺如（右侧缺如，左缩小）
  'xb-qr-lqr': 'jzx-12.png', //峡部缺如（左侧缺如）
  'xb-qr-lqr-rzc': 'jzx-12.png', //峡部缺如（左侧缺如，右正常）
  'xb-qr-lqr-rzd': 'jzx-12.png', //峡部缺如（左侧缺如，右增大）
  'xb-qr-lqr-rsx': 'jzx-12.png', //峡部缺如（左侧缺如，右缩小）

  'sx-xb-qr': 'jzx-21.png', //缩小-双侧-峡部缺如
  'sx-sc-xb-qr': 'jzx-21.png', //缩小-双侧-峡部缺如
  'sx-r-xb-qr': 'jzx-22.png', //缩小-右侧-峡部缺如
  'sx-l-xb-qr': 'jzx-23.png', //缩小-左侧-峡部缺如

  'lqr': 'jzx-24.png', //左侧缺如(右侧正常)
  'lqr-rzc': 'jzx-24.png', //左侧缺如(右侧正常)
  'lqr-rzd': 'jzx-25.png', //左侧缺如(右侧肿大)
  'lqr-rsx': 'jzx-26.png', //左侧缺如(右侧缩小)

  'rqr': 'jzx-27.png', //右侧缺如(左侧正常)
  'rqr-lzc': 'jzx-27.png', //右侧缺如(左侧正常)
  'rqr-lzd': 'jzx-28.png', //右侧缺如(左侧肿大)
  'rqr-lsx': 'jzx-29.png', //右侧缺如(左侧缩小)
}

// 病灶位置
var jzxPosObj = {
       // [[zc正常][sx缩小][zd增大]]
  'r-sj': [[39,61],[50,69],[29,50]],  //右叶-上极
  'r-zb': [[43,94],[53,92],[37,88]],  //右叶-中部
  'r-zsb': [[43,83],[53,82],[37,74]],  //右叶-中上部
  'r-zxb': [[43,105],[53,102],[37,102]],  //右叶-中下部
  'r-xj': [[51,124],[59,116],[45,125]],  //右叶-下极

  'l-sj': [[129,61],[118,69],[139,50]],  //左叶-上极
  'l-zb': [[125,94],[115,92],[131,88]],  //左叶-中部
  'l-zsb': [[125,84],[115,82],[131,74]],  //左叶-中上部
  'l-zxb': [[125,105],[115,102],[131,102]],  //左叶-中下部
  'l-xj': [[117,124],[109,116],[123,125]],  //左叶-下极

  'xb': [[84,102]],  //峡部-上极
  'xb-sj': [[84,102]],  //峡部-上极
  'xb-zb': [[84,102]],  //峡部-中部
  'xb-zsb': [[84,102]],  //峡部-中上部
  'xb-zxb': [[84,102]],  //峡部-中下部
  'xb-xj': [[84,102]],  //峡部-下极

  'zzy': [[84,62]],  //锥状叶-上极
  'zzy-sj': [[84,62]],  //锥状叶-上极
  'zzy-zb': [[84,62]],  //锥状叶-中部
  'zzy-zsb': [[84,62]],  //锥状叶-中上部
  'zzy-zxb': [[84,62]],  //锥状叶-中下部
  'zzy-xj': [[84,62]],  //锥状叶-下极
}

// 淋巴结高亮位置
var lbjHighCoord = {
  'l-IA': {
    text: 'IA',
    textCord: [94,72],
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[90,38],[99,49],[105,69],[109,97],[90,100]]
  },
  'l-IB': {
    text: 'IB',
    textCord: [104,55],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[90,38],[99,40],[118,52],[112,69],[109,97],[105,69],[99,49]]
  },
  'l-IIA': {
    text: 'IIA',
    textCord: [112,72],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[118,52],[127,49],[140,39],[126,63],[120,94],[109,97],[112,69]]
  },
  'l-IIB': {
    text: 'IIB',
    textCord: [125,69],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[140,39],[157,32],[140,63],[133,90],[120,94],[126,63]]
  },
  'l-III': {
    text: 'III',
    textCord: [117,105],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[109,97],[120,94],[133,90],[131,123],[107,129]]
  },
  'l-IV': {
    text: 'IV',
    textCord: [115,136],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[107,129],[131,123],[134,152],[107,154],[97,159],[103,146]]
  },
  'l-VA': {
    text: 'VA',
    textCord: [136,83],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[157,32],[172,38],[158,60],[148,88],[146,112],[131,123],[133,90],[140,63]]
  },
  'l-VB': {
    text: 'VB',
    textCord: [136,129],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[131,123],[146,112],[154,140],[134,152]]
  },
  'r-IA': {
    text: 'IA',
    textCord: [78,72],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[90,38],[90,100],[71,97],[75,69],[81,49]]
  },
  'r-IB': {
    text: 'IB',
    textCord: [68,55],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[62,52],[81,40],[90,38],[81,49],[75,69],[71,97],[68,69]]
  },
  'r-IIA': {
    text: 'IIA',
    textCord: [58,72],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[40,39],[53,49],[62,52],[68,69],[71,97],[60,94],[54,63]]
  },
  'r-IIB': {
    text: 'IIB',
    textCord: [45,69],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[23,32],[40,39],[54,63],[60,94],[47,90],[40,63]]
  },
  'r-III': {
    text: 'III',
    textCord: [56,105],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[47,90],[60,94],[71,97],[73,129],[49,123]]
  },
  'r-IV': {
    text: 'IV',
    textCord: [57,136],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[49,123],[73,129],[77,146],[83,159],[73,154],[46,152]]
  },
  'r-VA': {
    text: 'VA',
    textCord: [33,83],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[8,38],[23,32],[40,63],[47,90],[49,123],[34,112],[32,88],[22,60]]
  },
  'r-VB': {
    text: 'VB',
    textCord: [33,129],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[34,112],[49,123],[46,152],[26,140]]
  },
  'jb-VI': {
    text: 'VI',
    textCord: [86,121],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[71,97],[90,100],[109,97],[107,129],[103,146],[97,159],[90,161],[83,159],[77,146],[73,129]]
  },
  'jb-VII': {
    text: 'VII',
    textCord: [85,167],  //高亮文字的坐标
    strokeStyle: '#1885F2',   //边框线条色
    fillStyle: '#D1E7FC',   //区域填充色
    coords: [[73,154],[83,159],[90,161],[97,159],[107,154],[113,180],[67,180]]
  },
}
