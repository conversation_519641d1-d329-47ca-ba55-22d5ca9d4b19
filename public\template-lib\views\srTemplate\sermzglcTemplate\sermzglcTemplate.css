/* 间距 */
.n-pad {
  padding: 0!important;
}
.pd-8 {
  padding: 8px!important;
}
.pd8-12 {
  padding: 8px 12px;
}
.pd6-12 {
  padding: 6px 0 6px 12px;;
}
.mb-2 {
  margin-bottom: 2px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-6 {
  margin-left: 6px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-14 {
  margin-left: 14px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-26 {
  margin-left: 26px;
}
.ml-30 {
  margin-left: 30px;
}
.ml-34 {
  margin-left: 34px;
}
.ml-38 {
  margin-left: 38px;
}
.ml-48 {
  margin-left: 48px;
}
/* 字体 */
.f-bold {
  font-weight: bold;
}
.f-bold-black {
  font-weight: bold;
  color: #000;
}
/* 宽度 */
.un-wd {
  width: unset!important;
}
.wd-r-70 {
  width: 70px!important;
  text-align: right;
}
.wd-r-98 {
  width: 98px!important;
  text-align: right;
}
.wd-84 {
  width: 84px!important;
}
.wd-96 {
  width: 96px!important;
}
.wd-144 {
  width: 144px!important;
}
.wd-160 {
  width: 160px!important;
}
/* 高度 */
.lh-20 {
  line-height: 20px!important;
}
.lh-28 {
  height: 28px!important;
  line-height: 28px!important;
}
.lh-36 {
  line-height: 36px;
}
/* 颜色 */
.deep-gray {
  background: #EBEEF5!important;
}
/* 边框 */
.n-bor {
  border: none!important;
}
.bor-line {
  border: 1px solid #C8D7E6;
}
.bor-b {
  border-bottom: 1px solid #C8D7E6;
}
.bor-l {
  border-left: 1px solid #C8D7E6;
}
/* 主框架 */
#sermzglc1 {
  position: relative;
  height: 100%;
  width: 100%;
  color: #303133;;
  font-size: 14px;
  padding-top: 40px;
}
#sermzglc1 .w-auto {
  width: 960px;
  margin: 0 auto;
}
#sermzglc1 .sermzglc-h {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: #E8F3FF;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #C8D7E6;
  color: #000;
}
#sermzglc1 .sermzglc-edit {
  position: relative;
  height: 100%;
  background: #fff;
}
#sermzglc1 .rpt-con {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
#sermzglc1 .sermzglc-b {
  flex: 1;
  overflow: auto;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#sermzglc1 .sermzglc-f {
  height: 115px;
  padding: 8px 16px;
  background: #F5F7FA;
  border-top: 1px solid #DCDFE6;
  line-height: 22px;
}
#sermzglc1 .con-flex,#sermzglc1 .c-item {
  display: flex;
}
#sermzglc1 .flex-item {
  flex: 1;
}
#sermzglc1 .inp-sty {
  width: 60px;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 3px;
}
#sermzglc1 .tab-ls {
  line-height: 40px;
  background: #F5F7FA;
  border-bottom: 1px solid #DCDFE6;
}
#sermzglc1 .tab-i {
  display: inline-block;
  padding: 0 16px 0 12px;
  border-right: 1px solid #DCDFE6;
  text-align: center;
  cursor: pointer;
}
#sermzglc1 .tab-i.act {
  position: relative;
  background: #fff;
  color: #1885F2;
  font-weight: bold;
}
#sermzglc1 .tab-i.act::after {
  position: absolute;
  content: '';
  height: 1px;
  left: 0;
  right: 0;
  bottom: -1px;
  background: #fff;
}
#sermzglc1 #sermzg1 {
  padding: 12px;
}
#sermzglc1 .m-body .tab-con {
  display: none;
}
#sermzglc1 .m-body .tab-con.act {
  display: block;
}
#sermzglc1 .item-txt {
  width: 70px;
  line-height: 36px;
  text-align: right;
}
#sermzglc1 .item-box {
  flex: 1;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
  padding: 4px 0 4px 12px;
}
#sermzglc1 .add-btn {
  padding: 4px 8px;
  background: #1885F2;
  border-radius: 3px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
}
.close-icon:hover {
  width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 50%;
  background: #e1e1e1;
  cursor: pointer;
}
#sermzglc1 .add-btn img,#sermzglc1 .close-icon img {
  vertical-align: middle;
}
#sermzglc1 .bz-tit-ls {
  width: 761px;
  height: 32px;
  line-height: 32px;
  overflow-x: auto;
  overflow-y: hidden;
  background: #F5F7FA;
  border-top: 1px solid #C8D7E6;
  border-bottom: 1px solid #C8D7E6;
  display: flex;
  display: -webkit-box;
  display: -moz-box;
}
#sermzglc1 .bz-tit-ls::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
#sermzglc1 .bz-tit-i {
  display: flex;
  padding: 0 8px 0 12px;
  border-right: 1px solid #C8D7E6;
  text-align: center;
  cursor: pointer;
}
#sermzglc1 .bz-tit-i.act {
  position: relative;
  color: #1885F2;
  font-weight: bold;
}
#sermzglc1 .bz-tit-i.act::after {
  position: absolute;
  content: '';
  bottom: 1px;
  left: 0;
  right: 0;
  height: 1px;
  background: #F5F7FA;
}
#sermzglc1 .bz-con {
  padding: 8px 0;
}
#sermzglc1 .sermzglc-edit .bz-item {
  display: none;
  padding: 8px 12px;
}
#sermzglc1 .sermzglc-edit .bz-item.act {
  display: block;
}
#sermzglc1 .sub-box {
  padding: 4px 0 4px 8px;
}
#sermzglc1 .sub-lab-item {
  position: relative;
  width: 80px;
  height: 28px;
  line-height: 28px;
  background: #F5F7FA;
  padding: 0 8px;
}
#sermzglc1 .sub-lab-item::after {
  position: absolute;
  content: '';
  height: 27px;
  width: 1px;
  top: 0;
  right: -1px;
  background: #F5F7FA !important;
}
#sermzglc1 .sub-box-item {
  background: #F5F7FA!important;
  padding: 8px 12px;
}
#sermzglc1 .nm-item-box {
  flex: 1;
  border-left: 1px solid #C8D7E6!important;
  background: #F5F7FA!important;
}
#sermzglc1 .imp-txt {
  font-weight: bold;
  font-size: 18px;
  line-height: 28px;
  margin-right: 8px;
}
#sermzglc1 .sermzglc-f .footer-edit {
  padding: 0 12px 0 16px;
}
#sermzglc1 .imp-inp {
  width: 100%!important;
  height: 100px;
}

/* 修改w-block公共样式 */
#sermzglc1 .hight-item-pad {
  padding: 8px 0 4px 12px;
}
#sermzglc1 .w-block, #sermzglc1 .w-block-pd {
  background: #EBEEF5!important;
}
#sermzglc1 .w-block .light-block {
  width: 80px;
  padding-top: 4px;
}
#sermzglc1 #lcjbqk1 .w-block .light-block {
  width: 96px!important;
}
#sermzglc1 #lcjbqk1 .hight-block {
  padding: 4px 11px;
}
#sermzglc1 .w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::before, #sermzglc1 .w-block .lb-row.lb-active.on > input::before {
  width: unset!important;
  left: 0!important;
}
#sermzglc1 .w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::after, #sermzglc1 .w-block .lb-row.lb-active.on > input::after {
  right: -9px!important;
  border-top: 1px solid #C8D7E6!important;
  border-bottom: 1px solid #C8D7E6!important;
  background: #F5F7FA!important;
}
#sermzglc1 .w-block .hight-block {
  padding: 0;
  background: #F5F7FA!important;
}

/* 卵巢 */
#sermzglc1 .lc-tab-ls {
  height: 40px;
  line-height: 40px;
  overflow-x: auto;
  overflow-y: hidden;
  background: #F5F7FA;
  border-bottom: 1px solid #C8D7E6;
  display: flex;
  display: -webkit-box;
  display: -moz-box;
}
#sermzglc1 .lc-tab-ls::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
#sermzglc1 .lc-tab-i {
  display: flex;
  padding: 0 12px;
  border-right: 1px solid #C8D7E6;
  text-align: center;
  cursor: pointer;
}
#sermzglc1 .lc-tab-i.act {
  position: relative;
  color: #1885F2;
  font-weight: bold;
}
#sermzglc1 .lc-tab-i.act::after {
  position: absolute;
  content: '';
  bottom: 1px;
  left: 0;
  right: 0;
  height: 1px;
  background: #F5F7FA;
}
#sermzglc1 .sermzglc-edit .lc-tab-con,#sermzglc1 .sermzglc-edit .lcfjbb-con {
  display: none;
  padding: 8px 12px;
}
#sermzglc1 .sermzglc-edit .lc-tab-con.act,#sermzglc1 .sermzglc-edit .lcfjbb-con.act {
  display: block;
}
#sermzglc1 .lc-ms-box {
  display: flex;
  flex: 1;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#sermzglc1 .lc-ms-h {
  flex-grow: 1;
  padding: 5px 0 0 8px;
  border-bottom: 1px solid #C8D7E6;
  font-weight: bold;
}
#sermzglc1 .lc-ms-b {
  padding: 4px 0 4px 12px;
}
/* 积液 */
#sermzglc1 #sermjy1 {
  padding: 8px 14px;
}