$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var allResData = [];
var bzTitleClone = null;   //作为病灶示例模板的title
var bzConClone = null;  //作为病灶示例模板具体内容
var entryType = '';
var publicInfo = {};
// 侵犯部位键值集合
var ljOrganKeys = [
  {name: '神经血管束', key: 'xtsjsg', sideKey: 'xtsjsgPos'},
  {name: '精囊腺', key: 'xtjnx', sideKey: 'xtjnxPos'},
  {name: '膀胱', key: 'xtpg', descKey: 'mrqlxSms-rt-37'},
  {name: '尿道外括约肌', key: 'xtndwk', descKey: 'mrqlxSms-rt-42'},
  {name: '直肠', key: 'xtzc', descKey: 'mrqlxSms-rt-47'},
  {name: '肛提肌', key: 'xtgtj', sideKey: 'xtgtjPos'},
  {name: '盆壁', key: 'xtpb'},
];
// 错误反馈回调处理
function errorCallBack(id) {
  var curDom = curElem.find('[id="'+id+'"]').parents('.bz-item');
  if(curDom && curDom.length) {
    var key = curDom.attr('tab-target');
    curDom.closest('.bz-wrap').find('.bz-tit-i').removeClass('act');
    curDom.closest('.bz-wrap').find('.bz-tit-i[tab-id="'+key+'"]').addClass('act');
    curDom.closest('.bz-wrap').find('.bz-item').removeClass('act');
    curDom.addClass('act');
  }
}

function pageInit() {
  // 初始化将病灶参照代码复制存储起来
  initBzCloneEle();
  // 初始化扇区转canvas
  initCanvas();
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);
  // 切换病灶
  toggleBzHandler();
  // 评分
  setScoreHandler();
  // 病灶部位图事件
  clickOrganHight();

  // 通过选项确定图片的高亮情况
  toggleOrganImgByRadio();

  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    curElem.find('.def-ck').click();
  } else {
    displayBzContent();
  }
  
  // getAllDescAndImpContent();
  if(entryType !== 'view') {
    $('#mrqlxSms1').on('change', '.othercon .rt-sr-w', function() {
      if($(this).hasClass('bz-size')) {
        var size = $(this).val();
        if(size >= 1.5) {
          var parentTab = $(this).closest('.bz-item').attr('tab-target');
          var bzType = $('.bz-list [tab-id="'+parentTab+'"] .bz-type').text();
          curElem.find(`.${bzType}-score`).find('.hight-score').each(function(i, dom) {
            if(!$(dom).is(':checked')) {
              curElem.find(`.${bzType}-score`).find('.hight-score').click();
            }
          })
        }
      }
      getImpContent();
    })
  } else {
    getCloudFilmQrCodeUrl(publicInfo.examNo, $('.qlx-view .rt-sr-header'), '扫码查看云影像');
    // 预览页面回显
    initViewPageCon();
  }
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: curElem.find('.qlx-view')[0],  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    entryType = rtStructure.enterOptions.type;
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    if(publicInfo.sex !== '男') {
      curElem.find('#mrqlxSms1').html(`<h2 style="padding-left:20px;">此模板为前列腺报告，不适用于女性患者</h2>`);
      return;
    }
    setTimeout(() => {
      pageInit(); 
    }, entryType !== 'view' ? 100 : 0)
  }
}

// 切换病灶
function toggleBzHandler() {
  $('.bz-wrap').on('click', '.bz-tit-i', function(e) {
    var target = $(this).attr('tab-id');
    $(this).siblings('.bz-tit-i').removeClass('act');
    $(this).addClass('act');
    $('.bz-item[tab-target="'+target+'"]').siblings('.bz-item').removeClass('act');
    $('.bz-item[tab-target="'+target+'"]').addClass('act');
  })
}

function displayBzContent() {
  // 回显病灶
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
  if(allResData && allResData.length) {
    var allHightOrgan = [];
    var bzData = allResData.filter(bzItem => bzItem.id === 'mrqlxSms-bzlist-1')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('mrqlxSms-rt-', '');
        var bzType = item.val.includes('外周带') ? 'PZ' : 'TZ';
        // 高亮图查找
        if(item.child && item.child.length) {
          var organItem = item.child.filter(iOrgan => iOrgan.id === `mrqlxSms-rt-${paneId}-02`);
          if(organItem && organItem[0] && organItem[0].val) {
            allHightOrgan = [...allHightOrgan, ...JSON.parse(organItem[0].val)];
          }
        }
        addBzHandler(bzType, paneId)
      })
      if(allHightOrgan && allHightOrgan.length) {
        // 回显部位高亮情况
        organHightHandler(allHightOrgan)
      }
    }
  } else {
    curElem.find('.bz-list .bz-wrap').hide();
  }
}

// 复制暂存病灶的具体模板示例内容，为后续添加病灶做准备
function initBzCloneEle() {
  var bzTitle = curElem.find('.bz-list .bz-wrap .bz-tit-i').clone(true);  //作为病灶示例模板的title
  bzTitleClone = '<div class="bz-tit-i act" tab-id="mrqlxSms-rt-000">'+bzTitle.html()+'</div>';
  var bzCon = curElem.find('.bz-list .bz-wrap .bz-item').clone(true);
  bzConClone = '<div class="bz-item act" tab-target="mrqlxSms-rt-000">'+bzCon.html()+'</div>';
  curElem.find('.bz-list .bz-wrap .bz-tit-ls').html('');
  curElem.find('.bz-list .bz-wrap .bz-con').html('');
  // 预览下的部位高亮图
  if(entryType === 'view') {
    $('.qlx-edit .rpt-img *').addClass('follow-break');
    $('.qlx-view .rpt-img').html($('.qlx-edit .rpt-img').html());
  }
}

// 删除病灶
var willBzVm = null;
function delTab(vm, paneId) {
  willBzVm = vm;
  var dialogContent = `<div style="">确认删除该病灶?</div>`;
  dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  dialogContent += '<button onclick="removeBzHandler(\''+paneId+'\')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  dialogContent += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消</button>';
  dialogContent += '</div>';
  drawDialog({
    title: '提示',
    content: dialogContent,
    modal: true
  })
}
// 确认删除病灶
function removeBzHandler(paneId) {
  var vm = willBzVm;
  var idx = $('.bz-list .bz-tit-i .close-icon').index(vm);
  var isAct = $(vm).closest('.bz-tit-i').hasClass('act');
  $('.bz-list .bz-tit-i:eq('+idx+')').remove();
  $('.bz-list .bz-item:eq('+idx+')').remove();
  var resetBzLen = $('.bz-list .bz-item').length;
  if(resetBzLen > 0 && isAct) {
    let nextId = idx >= 1 ? idx - 1 : idx;
    $('.bz-list .bz-tit-i:eq('+nextId+')').addClass('act');
    $('.bz-list .bz-item:eq('+nextId+')').addClass('act');
  }
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
  removeRtDialog();
  if(resetBzLen > 0) {
    $('.bz-list .bz-name').each(function(i, dom) {
      var bzType = $(dom).siblings('.bz-type').attr('bz-type');
      var bzTypeLen = $('[bz-type="'+bzType+'"]').index($(dom).siblings('.bz-type'));
      $(dom).text((bzType === 'PZ' ? '外周带' : '移行带') + '病灶' + (bzTypeLen+1));
      // 修改病灶的颜色
      var colorIndex = ((bzTypeLen+1) % 4) === 0 ? 4 : ((bzTypeLen+1) % 4);
      var bzColorObj = colorFlagMap[bzType || 'PZ'][colorIndex - 1];
      bzColor = bzColorObj.color + '-' + bzColorObj.bgColor;
      $(dom).siblings('.bz-color').attr(bzColor);
      var [border, bg] = bzColor.split('-');
      $(dom).siblings('.bz-mark').css({
        'border-color': border,
        'background': bg,
      });
    });
    $('.bz-list .bz-wrap').show();
  } else {
    $('.bz-list .bz-wrap').hide();
  }
  // 处理所属的高亮病灶
  removeHightLightAreaByBz(`mrqlxSms-rt-${paneId}`)
  // getAllDescAndImpContent();
}

// 处理所属的高亮病灶
function removeHightLightAreaByBz(bzId) {
  // 精囊腺、尿道括约肌部
  curElem.find('img[data-bz]').each(function(i, img) {
    var dataBz = $(img).attr('data-bz');
    if(dataBz === bzId) {
      $(img).attr('data-bz', '').hide();
      $(img).siblings('img').show();
    }
  })

  // 属于canvas的高亮区域
  for(var key in sectorArea) {
    var polygonVertices = sectorArea[key].polygonVertices;
    for(var vertices of polygonVertices) {
      if(!vertices.checked || !vertices.belongBz || vertices.belongBz !== bzId) {
        continue;
      }
      vertices.belongBz = '';
      vertices.checked = false;  //是否已高亮
      vertices.belongBz = '';  //所属病灶
      vertices.bzColor = '';  
      clearCanvasLightArea(vertices);
    }
  }
}

// 添加病灶,oldPaneId已保存过的
function addBzHandler(type, oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzTypeLen = type ? $('.bz-list .bz-tit-ls [bz-type="'+type+'"]').length : 0;
  var activeTab = 'mrqlxSms-rt-' + paneId;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  var {newPaneBlock, bzColor} = appendBzHtml(type, paneId, bzTypeLen, oldIdAndDom);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // 病灶类型，划分是外周带还是移行带
    rtStructure.idAndDomMap[activeTab+'-0'] = {
      id: activeTab+'-0',
      desc: '病灶类型',
      name: '病灶类型',
      pid: activeTab,
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? rtStructure.idAndDomMap[activeTab+'-0'].value : type,
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // 病灶高亮色
    rtStructure.idAndDomMap[activeTab+'-01'] = {
      id: activeTab+'-01',
      desc: '病灶高亮色',
      name: '病灶高亮色',
      pid: activeTab,
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? rtStructure.idAndDomMap[activeTab+'-01'].value : bzColor,
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // 部位高亮图
    rtStructure.idAndDomMap[activeTab+'-02'] = {
      id: activeTab+'-02',
      desc: '病灶部位高亮图',
      name: '病灶部位高亮图',
      pid: activeTab,
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value && rtStructure.idAndDomMap[activeTab+'-02'] ? rtStructure.idAndDomMap[activeTab+'-02'].value : '',
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '病灶',
      name: '病灶title',
      pid: 'mrqlxSms-bzlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ((type === 'PZ'?'外周带':'移行带') + '病灶' +(bzTypeLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('.bz-list .bz-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
    // if(!oldPaneId) {
    //   getAllDescAndImpContent();
    // }
    document.querySelector(".bz-list .bz-wrap .bz-tit-ls").scrollLeft = document.querySelector(".bz-list .bz-wrap .bz-tit-ls").scrollWidth;
  }
  if(type) {
    curElem.find('.bz-dropmenu').hide();
  }
}

// 处理新增病灶的html
function appendBzHtml(type, paneId, bzTypeLen, oldIdAndDom) {
  $('.bz-list .bz-tit-ls .bz-tit-i').removeClass('act');
  $('.bz-list .bz-con .bz-item').removeClass('act');
  var reg = new RegExp('000', 'ig');
  var title = bzTitleClone.replace(reg, paneId);   //作为病灶示例模板的title
  var content = bzConClone.replace(reg, paneId);  //作为病灶示例模板具体内容
  $('.bz-list .bz-tit-ls').append(title);
  $('.bz-list .bz-con').append(content);
  var newPaneBlock = $('.bz-list .bz-item[tab-target="mrqlxSms-rt-'+paneId+'"]');
  $('.bz-list .bz-tit-i[tab-id="mrqlxSms-rt-'+paneId+'"]').find('.bz-name').text(oldIdAndDom && oldIdAndDom.value ? oldIdAndDom.value : ((type === 'PZ'?'外周带':'移行带')+'病灶'+(bzTypeLen+1)));
  var bzType = oldIdAndDom && oldIdAndDom.value ? rtStructure.idAndDomMap['mrqlxSms-rt-'+paneId+'-0'].value : type;
  $('.bz-list .bz-tit-i[tab-id="mrqlxSms-rt-'+paneId+'"]').find('.bz-type').text(bzType);
  $('.bz-list .bz-tit-i[tab-id="mrqlxSms-rt-'+paneId+'"]').find('.bz-type').attr('bz-type', bzType);
  var bzColor = oldIdAndDom && oldIdAndDom.value ? rtStructure.idAndDomMap['mrqlxSms-rt-'+paneId+'-01'].value : '';
  // 新的病灶
  if(!bzColor) {
    var colorIndex = ((bzTypeLen+1) % 4) === 0 ? 4 : ((bzTypeLen+1) % 4);
    var bzColorObj = colorFlagMap[bzType || 'PZ'][colorIndex - 1];
    bzColor = bzColorObj.color + '-' + bzColorObj.bgColor;
  }
  $('.bz-list .bz-tit-i[tab-id="mrqlxSms-rt-'+paneId+'"]').find('.bz-color').text(bzColor);
  var [border, bg] = bzColor.split('-');
  $('.bz-list .bz-tit-i[tab-id="mrqlxSms-rt-'+paneId+'"]').find('.bz-mark').css({
    'border-color': border,
    'background': bg,
  });
  var bzOrgan = oldIdAndDom && oldIdAndDom.value ? rtStructure.idAndDomMap['mrqlxSms-rt-'+paneId+'-02'].value : '';
  $('.bz-list .bz-tit-i[tab-id="mrqlxSms-rt-'+paneId+'"]').find('.bz-organ').text(bzOrgan);
  return {newPaneBlock, bzColor};
}

// 病灶的交互
function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    newPaneBlock.find('.def-ck').click();
  }
  // // 块之间的高亮
  // toggleHightBlock(newPaneBlock, true);
  // initLigthItemChange(newPaneBlock);
}
// 展开添加病灶的下拉框
function showBzDropmenu() {
  curElem.find('.bz-dropmenu').toggle();
}

// 外周带和移行带评分
function setScoreHandler(change) {
  if(!change) {
    $('.set-score').on('change', '.score-tg,[name="wzddce"]', function() {
      setScoreHandler(true)
    })
  }
  $('.set-score .score-tg').each(function(i, dom) {
    var curArea = $(dom).hasClass('parscore-ck') ? $(dom).attr('id') : $(dom).closest('[contect-id]').attr('contect-id');
    var sigleScore = $('label[for="'+curArea+'"]').find('.sigle-score');
    if($(dom).is(':checked')) {
      if(!$(dom).hasClass('parscore-ck')) {
        var score = $(dom).val().trim();
        sigleScore.text(score).show();
      }
    } else {
      if($(dom).hasClass('parscore-ck')) {
        sigleScore.text('').hide();
      }
    }
  })
  // 总分看主要序列，外周带看DWI，移行带看T2WI
  var scoreKey = {
    'mrqlxSms-wzd-rt-8': 'mrqlxSms-wzd-rt-3',  //外周带
    'mrqlxSms-yxd-rt-8': 'mrqlxSms-yxd-rt-5',  //移行带
  }
  $('.score-wrap .total-score').each(function(i, dom) {
    var score = 0;
    var id = $(dom).find('.score').attr('id');
    $(dom).siblings('.score-detail').find('.sigle-score[id="'+scoreKey[id]+'"]').each(function(j, sDom) {
      if($(sDom).text().trim()) {
        score += Number($(sDom).text());
      }
    })
    // 外周带DWI=3时，DCE阳性，分数+1
    if(id === 'mrqlxSms-wzd-rt-8' && score === 3 && $('[name="wzddce"]:checked').val() === '有早期异常强化') {
      score = score + 1;
    }
    // 移行带
    if(id === 'mrqlxSms-yxd-rt-8') {
      if(score === 2 && $('#mrqlxSms-yxd-rt-3').text().trim() >= 4) {
        score = score + 1;
      }else if(score === 3 && $('#mrqlxSms-yxd-rt-3').text().trim() >= 5) {
        score = score + 1;
      }
    }
    if(score > 0) {
      $(dom).find('.score').text(score + '分');
      $(dom).show();
    } else {
      $(dom).find('.score').text('');
      $(dom).hide();
    }
  })
}

// 部位图高亮回显
function organHightHandler(allData) {
  allData.forEach(item => {
    var data = sectorArea[item.pid]?.polygonVertices;
    if(data && data.length) {
      for(var child of data) {
        if(child.checked) {
          continue;
        }
        if(child.code === item.code) {
          child.checked = true;
          child.belongBz = item.belongBz;
          child.bzType = item.bzType;
          child.bzColor = item.bzColor;
          highLightOrgan(child, {}, true);
          break;
        }
      }
    }
  })
}

var imgAndRadioFlag = false;
// 通过选项确定图片的高亮情况
function toggleOrganImgByRadio() {
  // 初始化
  setJnxAndNdImgAndSel('sv', false);
  setJnxAndNdImgAndSel('ut', false);

  $('.qlx-edit .img-radio').on('change', '.rt-sr-w', function(e) {
    if(!imgAndRadioFlag) {
      var imgType = $(this).closest('.img-radio').attr('img-type');
      setJnxAndNdImgAndSel(imgType, false);
    } else {
      imgAndRadioFlag = false;
    }
  })
}

// 精囊腺和尿道外括约肌受累
function setJnxAndNdImgAndSel(imgType, fromImg) {
  // 点击图片确定选项
  if(fromImg) {
    imgAndRadioFlag = true;
    if(!$(`img.${imgType}.on:visible`).length) {
      curElem.find(`input.${imgType}[value="无"]`).click();
    } else {
      curElem.find(`input.${imgType}[value="有"]`).click();
      if(imgType === 'sv') {
        var side = $(`img.${imgType}.on:visible`).length === 2 ? '双侧' : '';
        if(!side) {
          side = $(`img.${imgType}.on:visible`).attr('img-info').split(',')[1];
        }
        curElem.find(`input[name="xtjnxPos"][value="${side}"]`).click();
      }
    }
  } else {
    // 选项确定图像
    var parentId = entryType === 'view' ? '.qlx-view' : '.qlx-edit';
    var select = curElem.find(`input.${imgType}:checked`).val();
    curElem.find(`${parentId} img.${imgType}.off`).show();
    curElem.find(`${parentId} img.${imgType}.on`).hide();
    if(select === '有') {
      if(imgType === 'sv') {
        var side = curElem.find(`input[name="xtjnxPos"]:checked`).val();
        if(side === '双侧') {
          curElem.find(`${parentId} img.sv.on`).show();
          curElem.find(`${parentId} img.sv.off`).hide();
        } else {
          var sideImg = curElem.find(`${parentId} [img-info="sv,${side}"]`);
          sideImg.show();
          sideImg.siblings('img').hide();
        }
      } else {
        curElem.find(`${parentId} img.ut.on`).show();
        curElem.find(`${parentId} img.ut.off`).hide();
      }
    } 
  }
}

// 部位图点击高亮
function clickOrganHight() {
  $('.qlx-edit').on('click', '.rpt-img img, .rpt-img canvas', function(e) {
    var vm = $(this);
    if(vm.hasClass('off') || vm.hasClass('on')) {
      var imgInfo = vm.siblings('img').attr('img-info');
      var [code, side] = imgInfo.split(',');
      vm.hide();
      vm.siblings('img').show();
      setJnxAndNdImgAndSel(code, true);
    } else {
      var activeBz = $('.bz-list .bz-tit-i.act');
      if(!activeBz.length) {
        $wfMessage({
          type: 'warn',
          content: '请先添加病灶'
        });
        return;
      }
      var bzId = activeBz.attr('tab-id');
      var bzOrgan = activeBz.find('.bz-organ').text();
      var bzOrganArr = [];
      if(bzOrgan) {
        bzOrganArr = JSON.parse(bzOrgan);
      }
      var bzColor = activeBz.find('.bz-color').text();
      var bzType = activeBz.find('.bz-type').text();

      toggleCanvasHighLight(e, {
        activeBz,
        bzId,
        bzOrgan,
        bzOrganArr,
        bzColor,
        bzType
      })
    }
    // 填充各个病灶的具体定位
    fillBzAreaDetailOrgan();
  })
}

// 点击canvas高亮切换
function toggleCanvasHighLight(e, params) {
  if(entryType === 'view') {
    return;
  }
  // 当前病灶的信息
  var { activeBz, bzId, bzOrgan, bzOrganArr, bzColor, bzType } = params;
  var curCanvas = e.target;
  var [key, name] = curCanvas.getAttribute('img-info').split(',');
  var x = e.clientX - curCanvas.getBoundingClientRect().left;
  var y = e.clientY - curCanvas.getBoundingClientRect().top;
  var polygonVertices = sectorArea[key].polygonVertices;
  if(polygonVertices && polygonVertices.length) {
    var curOrgan = findPolygonByPoint(x, y, polygonVertices);
    if(curOrgan) {
      if(!curOrgan.checked) {
        if(['PZ', 'TZ'].includes(curOrgan.bzType)) {
          if(bzType !== curOrgan.bzType) {
            $wfMessage({
              type: 'warn',
              content: `${bzType==='PZ'?'外周带':'移行带'}病灶不可选择${bzType==='PZ'?'移行带':'外周带'}区域`
            });
            return;
          }
        }
        // 绘制
        highLightOrgan(curOrgan, params);
        bzOrganArr.push({
          code: curOrgan.code,
          organName: curOrgan.organName,
          bzColor: bzColor,
          bzType: bzType,
          pid: curOrgan.pid,
          belongBz: bzId
        })
        curOrgan.checked = true;
        curOrgan.belongBz = bzId;
        curOrgan.bzColor = bzColor;  
      } else {
        if(curOrgan.belongBz === bzId) {
          console.log(curOrgan.pid);
          bzOrganArr = bzOrganArr.filter(item => item.pid !== curOrgan.pid || item.code !== curOrgan.code);
          curOrgan.belongBz = '';
          curOrgan.checked = false;  //是否已高亮
          curOrgan.belongBz = '';  //所属病灶
          curOrgan.bzColor = '';  
          // 取消高亮
          clearCanvasLightArea(curOrgan);
        } else {
          $wfMessage({
            type: 'warn',
            content: '该病灶位于' + $('[tab-id="'+curOrgan.belongBz+'"] .bz-name').text()
          });
        }
      }
      activeBz.find('.bz-organ').text(bzOrganArr.length ? JSON.stringify(bzOrganArr) : '');
    }
  }
}

// 填充各个病灶的具体定位
function fillBzAreaDetailOrgan() {
  curElem.find('.bz-list .bz-tit-i .bz-organ').each(function(i, dom) {
    var organArr = $(dom).text() ? JSON.parse($(dom).text()) : [];
    var bzId = $(dom).closest('.bz-tit-i').attr('tab-id');
    if(organArr.length) {
      var bzListOrgan = organArr.map(item => item.organName).join('、');
      curElem.find('[tab-target="'+bzId+'"] .bzOrgan').val(bzListOrgan);
    } else {
      curElem.find('[tab-target="'+bzId+'"] .bzOrgan').val('');
    }
  })
}

// 初始化各个扇区canvas
function initCanvas() {
  var parentId = entryType === 'view' ? '.qlx-view' : '.qlx-edit';
  for(var type in sectorArea) {
    var item = sectorArea[type];
    var canvasEle = document.querySelector(`${parentId} #${type}Canvas`);
    item.ctx = canvasEle.getContext("2d");
    item.imageEle = document.querySelector(`${parentId} #${type}Image`);
    item.imageEle.style.display = "block";
    var w = item.imageEle.width;
    var h = item.imageEle.height;
    item.canvasW = w;
    item.canvasH = h;
    item.ctx.drawImage(item.imageEle, 0, 0, w, h); // 绘制画布
  }
}

// 判断在哪个扇区内
function findPolygonByPoint(x, y, polygonVertices) {
  var curOrgan = null;
  for(var polygon of polygonVertices) {
    if(!polygon['vertices']) {
      continue;
    }
    var isInside = false;
    var vertices = polygon['vertices'].map(item => {
      var [vX, vY] = item;
      return {x:vX, y:vY}
    })
    for (var i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
      var xi = vertices[i].x, yi = vertices[i].y;
      var xj = vertices[j].x, yj = vertices[j].y;
      // 检查点是否在边的上方，如果是，继续检查
      if ((yi > y) !== (yj > y)) {
        var intersect = (x < ((xj - xi) * (y - yi) / (yj - yi)) + xi);
        if (intersect) {
          isInside = !isInside;
        }
      }
    }
    if(isInside) {
      curOrgan = polygon;
      break;
    }
  }

  return curOrgan;
}

// 绘制扇区高亮
function highLightOrgan(curOrgan, params, redraw) {
  var { bzId, bzOrgan, bzOrganArr, bzColor, bzType } = params;
  if(redraw) {
    bzColor = curOrgan.bzColor;
  }
  var [strokeStyle, fillStyle] = bzColor.split('-');
  var parentOrgan = sectorArea[curOrgan.pid];
  var ctx = parentOrgan.ctx;
  ctx.save();
  var coords = curOrgan.vertices;
  if(coords && coords.length) {
    // 高亮背景和边框
    ctx.strokeStyle = strokeStyle; // 填充线颜色
    ctx.fillStyle = fillStyle; // 填充区域颜色
    ctx.lineWidth = 2;
    ctx.beginPath();
    for (var i = 0; i < coords.length; i++) {
      ctx.lineTo(coords[i][0], coords[i][1]);
    }
    ctx.closePath();
    ctx.stroke();
    ctx.fill();
  }
  
  // 高亮部位文字
  if(curOrgan.textCord && curOrgan.textCord.length) {
    ctx.beginPath();
    ctx.fillStyle = strokeStyle;
    ctx.font = curOrgan.pid === 'apex' ? "bold 12px Arial" : "bold 14px Arial";
    ctx.textBaseline = "top";
    ctx.fillText(curOrgan.text, curOrgan.textCord[0], curOrgan.textCord[1]);
    ctx.restore();
  }
}

// 清除高亮区域
function clearCanvasLightArea(curOrgan) {
  var parentOrgan = sectorArea[curOrgan.pid];
  // 清除之前的高亮
  parentOrgan.ctx.clearRect(0, 0, parentOrgan.canvasW, parentOrgan.canvasH);
  parentOrgan.ctx.drawImage(parentOrgan.imageEle, 0, 0, parentOrgan.canvasW, parentOrgan.canvasH); // 绘制画布
  let polygonVertices = parentOrgan.polygonVertices.filter(item => item.checked);  //获取高亮的区域，进行重绘
  // 重绘
  for(var area of polygonVertices) {
    highLightOrgan(area, {}, true);
  }
}

// 获取病灶的描述内容
function getBzInfoCon(type) {
  var bzInfoList = [];
  var bzDesc = [];
  $('.bz-list .bz-tit-ls .bz-tit-i').each(function(i, dom) {
    var bzItem = $(dom);
    var bzId = bzItem.attr('tab-id');
    var key = bzId.split('-')[2];
    var bzSize = (getVal(`[id="mrqlxSms-rt-${key}-7"]`) || '-') + 'cmx' + (getVal(`[id="mrqlxSms-rt-${key}-8"]`) || '-') + 'cm';
    var bzSeIm = '';
    if(getVal(`[id="mrqlxSms-rt-${key}-9"]`) || getVal(`[id="mrqlxSms-rt-${key}-10"]`)) {
      bzSeIm = '(SE'+(getVal(`[id="mrqlxSms-rt-${key}-9"]`) || '-') + '，IM' + (getVal(`[id="mrqlxSms-rt-${key}-10"]`) || '-') +')';
    }
    var obj = {
      bzId: bzId,
      bzSize: bzSize,
      bzSeIm: bzSeIm,
      bzName: bzItem.find('.bz-name').text(),
      bzColor: bzItem.find('.bz-color').text(),
      bzPos: getVal(`[id="mrqlxSms-rt-${key}-2"]`),
      bzFx: getVal(`[name="bzfx${key}"]`, `${type!=='view'?'~':'点-'}`),
      bzArea: bzSize + bzSeIm,
      bzXz: getVal(`[name="bzxz${key}"]:checked`),
      bzBy: getVal(`[name="bzby${key}"]:checked`),
      bzBm: getVal(`[name="bzbm${key}"]:checked`),
      bzLevel: getVal(`[name="bzbbfj${key}"]:checked`),
    }
    if(obj.bzFx) {
      obj.bzFx += (type!=='view' ? '点钟' : '点');
    }
    if(obj.bzLevel) {
      obj.viewLevel = obj.bzLevel === '主要病变' ? '主' : '次';
    }
    bzInfoList.push(obj);   //整合各个数据

    if(obj.bzPos) {
      var oneBz = [];
      var str = obj.bzName + '：' + obj.bzPos;
      obj.bzFx && (str += `约${obj.bzFx}方向`);
      obj.bzLevel && (str += `（${obj.bzLevel}）`);
      str && oneBz.push(str);
      
      obj.bzArea && oneBz.push('最大横截面积约' + obj.bzArea);
      obj.bzXz && oneBz.push('病灶呈' + obj.bzXz);
      obj.bzBy && oneBz.push('边缘' + obj.bzBy);
      obj.bzBm && oneBz.push('包膜' + obj.bzBm);

      oneBz.length && bzDesc.push(oneBz.join('，'));
    }
  })

  if(type === 'view') {
    return bzInfoList;
  } else {
    return bzDesc.length ? (bzDesc.join('。\n') + '。') : '';
  }
}

// 获取诊断
function getImpContent() {
  var imgArr = [];

  // 诊断第一行
  // 评分
  var scoreArr = ['PI-RADS(Version2.1)评分标准'];
  // 外周带
  var pzScore = getVal('[id="mrqlxSms-wzd-rt-8"]');
  if(pzScore) {
    pzScore = pzScore.replace('分', '');
  }
  $('[id="mrqlxSms-rt-102"]').val(pzScore || '');
  scoreArr.push(`外周带总评分：${pzScore || '-'}分`);
  // 移行带
  var tzScore = getVal('[id="mrqlxSms-yxd-rt-8"]');
  if(tzScore) {
    tzScore = tzScore.replace('分', '');
  }
  $('[id="mrqlxSms-rt-103"]').val(tzScore || '');
  scoreArr.push(`移行带总评分：${tzScore || '-'}分`);

  // 累及
  var ljCon = getLjDetailCon('imp');
  ljCon && scoreArr.push(ljCon);
  $('[id="mrqlxSms-rt-104"]').val(ljCon ? (ljCon.replace('累及', '')) : '');
  
  scoreArr.length && (imgArr.push(scoreArr.join('，')));

  // 第二行
  var lbImp = getVal('[name="lbjType"]:checked');
  lbImp && (imgArr.push('淋巴结：' + lbImp));
  $('[id="mrqlxSms-rt-105"]').val(lbImp);

  // 第三行
  var strList = [];
  var gzyStr = getVal('[name="gzyType"]:checked') === '有' ? '骨转移' : '';
  gzyStr && strList.push(gzyStr);
  $('[id="mrqlxSms-rt-106"]').text(gzyStr ? (gzyStr + '；') : '');
  var otherStr = getVal('[id="mrqlxSms-rt-100"]');
  otherStr && strList.push(otherStr);
  strList.length && (imgArr.push(strList.join('；')));

  var imgRes = imgArr.length ? imgArr.join('\n') : '';
  $('[id="mrqlxSms-rt-101"]').val(imgRes);
  return imgRes;
}

// 侵犯受累部位
function getLjDetailCon(type) {
  var arr = [];
  var sideMap = {
    '左侧': [],
    '右侧': [],
    '双侧': []
  }
  ljOrganKeys.forEach(function(item) {
    var checkStatus = getVal('[name="'+item.key+'"]:checked');
    if(checkStatus === '有') {
      var str = '';
      var viewStr = '';
      // 有具体部位的
      var flag = false;
      if(item.sideKey) {
        var side = getVal('[name="'+item.sideKey+'"]:checked');
        if(side) {
          sideMap[side].push(item.name);
          flag = true;
          entryType === 'view' && (viewStr += `（${side}）`);
        }
      }
      if(!flag) {
        str += item.name;
      }
      // 有描述内容的
      if(type === 'desc') {
        if(item.descKey) {
          var desc = getVal('[id="'+item.descKey+'"]');
          if(desc) {
            str += `（${desc}）`;
            entryType === 'view' && (viewStr += `（${desc}）`);
          }
        }
      }
      if(entryType === 'view') {
        arr.push(`<div>${item.name}受累：${checkStatus}${viewStr}</div>`)
      } else {
        str && arr.push(str);
      }
    } else {
      if(entryType === 'view') {
        arr.push(`<div>${item.name}受累：${checkStatus || '-'}</div>`)
      }
    }
  })

  if(entryType !== 'view') {
    for(var side in sideMap) {
      if(sideMap[side].length) {
        arr.unshift(side + sideMap[side].join('、'));
      }
    }
  }
  if(arr.length) {
    if(entryType !== 'view') {
      return (type === 'desc' ? '侵犯' : '累及') + arr.join('，') + (type === 'desc' ? '。' : '');
    } else {
      return arr.join('');
    }
  }
  return '';
}

// 获取描述
function getDescContent() {
  var descArr = [];
  // 整体评估
  var allPgCon = getAllPgCon();
  allPgCon && descArr.push(allPgCon);

  // 病灶
  var bzInfoCon = getBzInfoCon();
  bzInfoCon && descArr.push(bzInfoCon);

  // 外周带评分
  var pzScoreCon = getScoreCon('wzd');
  pzScoreCon && descArr.push(pzScoreCon);

  // 移行带评分
  var tzScoreCon = getScoreCon('yxd');
  tzScoreCon && descArr.push(tzScoreCon);

  // 腺体外表现
  var xtwCon = getLjDetailCon('desc');
  xtwCon && descArr.push(xtwCon);

  // 淋巴结
  var lbjCon = getLbjDetailCon();
  // 骨转移
  var gzy = getVal('[name="gzyType"][value="有"]:checked');
  if(gzy) {
    var gzyDesc = getVal('[id="mrqlxSms-rt-97"]') ? getVal('[id="mrqlxSms-rt-97"]') : '骨转移';
    lbjCon += gzyDesc + '。';
  }
  lbjCon && descArr.push(lbjCon);

  // 其他征象
  var otherDesc = getVal('[id="mrqlxSms-rt-99"]');
  otherDesc && descArr.push(otherDesc);

  return descArr.length ? (descArr.join('\n')) : '';
}

// 整体评估描述
function getAllPgCon() {
  var oneArr = [];
  // 大小
  var qlxSize = [];
  for(var i = 11; i <= 13; i++) {
    var val = getVal(`[id="mrqlxSms-rt-${i}"]`) || '-';
    qlxSize.push(val + 'cm');
  }
  var serArr = [];
  for(var i = 14; i <= 17; i++) {
    var val = getVal(`[id="mrqlxSms-rt-${i}"]`) || '-';
    serArr.push(([14,16].indexOf(i)>-1?'SE':'IM') + val);
  }
  oneArr.push('前列腺大小约'+ qlxSize.join('x') + `（${serArr.join('，')}）`);
  // 形态
  var qlxxt = getVal('[name="qlxxt"]:checked') || '-';
  oneArr.push('形态'+ qlxxt);
  // PSA水平
  var pas = getVal('[id="mrqlxSms-rt-19"]') || '-';
  oneArr.push('PSA水平'+ pas + 'ng/ml');
  if(oneArr.length) {
    return oneArr.join('，') + '。';
  }
  return '';
}

// 外周带/移行带评分描述
function getScoreCon(type) {
  var detailInfo = {};
  var descKeyArr = [
    {'selector': type === 'wzd' ? '[id="mrqlxSms-wzd-rt-3"]' : '[id="mrqlxSms-yxd-rt-5"]', 
      'prefix': type === 'wzd' ? 'DWI得分：' : 'T2WI得分：', 'subfix': '分',
      'detailKey': type === 'wzd' ? 'DWI' : 'T2WI'
    },

    {'selector': `[name="${type}dce"]:checked`, 'prefix': '动态增强后', 'detailKey': 'zqType'},
    {'selector': `[name="${type}dceType"]:checked`, 'prefix': '灌注曲线呈', 'detailKey': 'DCE'},
    {'selector': type === 'wzd' ? '[id="mrqlxSms-wzd-rt-5"]' : '[id="mrqlxSms-yxd-rt-3"]', 
      'prefix': type === 'wzd' ? 'T2WI得分：' : 'DWI得分：', 'subfix': '分',
      'detailKey': type === 'wzd' ? 'T2WI' : 'DWI'
    },
    {'selector': `[id="mrqlxSms-${type}-rt-8"]`, 'prefix': 'PI-RADS总评分：', 'detailKey': 'total'},
  ];//评分的顺序
  
  var descArr = [];
  for(var item of descKeyArr) {
    var value = getVal(item.selector);
    if(value) {
      descArr.push(`${item.prefix || ''}${value}${item.subfix || ''}`);
      if(item.detailKey === 'zqType') {
        detailInfo[item.detailKey] = value === '有早期异常强化' ? '有' : '无';
      } else {
        detailInfo[item.detailKey] = (value + `${item.subfix || ''}`) || '';
      }
    }
  }
  var str = '';
  if(descArr.length) {
    var typeName = type === 'wzd' ? '外周带' : '移行带';
    str += typeName + '评分：\n';
    str += descArr.join('，') + '。';
  }
  if(entryType !== 'view') {
    return str;
  } else {
    return detailInfo;
  }
}

// 淋巴结的描述内容
function getLbjDetailCon() {
  var lbjDesc = [];
  var qyLbj = ['前列腺周围', '髂内', '髂外（包括闭孔）', '骶前'];  //区域淋巴结
  var qyLbjData = {
    lbjArr: [],
    maxLbj: '',
    maxSize: '',  //最大者短径
    maxArea: '',  //最大者短径完整描述
    totalCount: 0
  }
  curElem.find('[name="lbjType"]:checked').each(function(i, dom) {
    var lbjItem = $(dom);
    var lbjId = $(dom).attr('id');
    var lbjName = lbjItem.val().replace('淋巴结', '');
    var lbjCount = getVal(`[pid="${lbjId}"].lbj-count`) || '数';  //数量
    var lbjSize = getVal(`[pid="${lbjId}"].lbj-size`);  //最大径
    var lbjSe = getVal(`[pid="${lbjId}"].lbj-se`);  //SE
    var lbjIm = getVal(`[pid="${lbjId}"].lbj-im`);  //IM
    if(entryType !== 'view') {
      if(qyLbj.includes(lbjName)) {
        qyLbjData.lbjArr.push(lbjName);
        if(!isNaN(Number(lbjCount))) {
          qyLbjData.totalCount += Number(lbjCount);  //淋巴数量
        }
        if(!qyLbjData.maxSize) {
          qyLbjData.maxSize = lbjSize;
          qyLbjData.maxLbj = lbjName;
          qyLbjData.maxArea = `最大者短径：${lbjSize || '-'}cm（Ser${lbjSe || '-'},Image${lbjIm || '-'}）`;
        } else {
          if(!isNaN(Number(lbjSize))) {
            if(Number(lbjSize) > Number(qyLbjData.maxSize)) {
              qyLbjData.maxSize = lbjSize;
              qyLbjData.maxLbj = lbjName;
              qyLbjData.maxArea = `最大者短径：${lbjSize || '-'}cm（Ser${lbjSe || '-'},Image${lbjIm || '-'}）`;
            }
          }
        }
      } else {
        var oneLbj = [];
        oneLbj.push(lbjName + '见' + lbjCount + '枚淋巴结');
        oneLbj.push('较大者位于' + lbjName);
        oneLbj.push(`最大者短径：${lbjSize || '-'}cm（Ser${lbjSe || '-'},Image${lbjIm || '-'}）`);
        lbjDesc.push(oneLbj.join('，'));
      }
    } else {
      var oneLbj = [lbjName + '淋巴结'];
      oneLbj.push(lbjCount + '枚');
      oneLbj.push(`最大者短径${lbjSize || '-'}cm（SE${lbjSe || '-'},IM${lbjIm || '-'}）`);
      lbjDesc.push(`<div>${oneLbj.join('，')}；</div>`);
    }
  })

  if(entryType !== 'view') {
    if(qyLbjData.lbjArr.length) {
      var oneLbj = [];
      var sub = qyLbjData.lbjArr[qyLbjData.lbjArr.length-1].indexOf('周围') > -1 ? '' : '周围';
      var name = qyLbjData.lbjArr.join('、') + sub;
      oneLbj.push(name + '见' + (qyLbjData.totalCount || '数') + '枚淋巴结');
      oneLbj.push('较大者位于' + qyLbjData.maxLbj);
      oneLbj.push(`${qyLbjData.maxArea}`);
      lbjDesc.unshift(oneLbj.join('，'));
    }
    return lbjDesc.length ? (lbjDesc.join('。') + '。') : '';
  } else {
    return lbjDesc.length ? (lbjDesc.join('')) : '';
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescContent();
  rtStructure.impression = getImpContent();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

// 预览页面回显
function initViewPageCon() {
  var idAndDomMap = rtStructure.idAndDomMap;
  var examInfo = rtStructure.examInfo;
  // 患者信息
  $('.qlx-view [data-id]').each(function(i, dom){
    var id = $(dom).attr('data-id');
    $(dom).text(idAndDomMap && idAndDomMap[id] ? (idAndDomMap[id].value || '') : (examInfo[id]) || '');
  })
  $('.qlx-view [data-img]').each(function(i, dom){
    var id = $(dom).attr('data-img');
    var value = idAndDomMap && idAndDomMap[id] ? (idAndDomMap[id].value || '') : (examInfo[id]) || '';
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  // 整体评估
  var ztpgText = '';
  ztpgText += `<div>前列腺形态：${getVal('[name="qlxxt"]:checked') || '-'}</div>`;
  ztpgText += `<div>前列腺测量：${getVal('[id="mrqlxSms-rt-11"]') || '-'}cmx${getVal('[id="mrqlxSms-rt-12"]') || '-'}cmx${getVal('[id="mrqlxSms-rt-13"]') || '-'}cm`;
  ztpgText += `（SE${getVal('[id="mrqlxSms-rt-14"]') || '-'},IM${getVal('[id="mrqlxSms-rt-15"]') || '-'},SE${getVal('[id="mrqlxSms-rt-16"]') || '-'},IM${getVal('[id="mrqlxSms-rt-17"]') || '-'}）</div>`;
  ztpgText += `<div>PSA水平：${getVal('[id="mrqlxSms-rt-19"]') || '-'}ng/ml</div>`;
  $('.qlx-view .ztpg .rpt-desc').html(ztpgText);

  // 侵犯受累
  var xtwbxText = getLjDetailCon('desc');
  $('.qlx-view .xtwbx .rpt-desc').html(xtwbxText);

  // 骨转移
  var gzyText = getVal('[name="gzyType"]:checked');
  if(gzyText === '有' && getVal('[id="mrqlxSms-rt-97"]')) {
    gzyText += `（${getVal('[id="mrqlxSms-rt-97"]')}）`
  }
  $('.qlx-view .gzy .rpt-desc').html(gzyText);
  
  // 淋巴结转移
  var lbjzyText = getLbjDetailCon();
  if(lbjzyText) {
    lbjzyText = '<div>'+lbjzyText+'</div>';
  }
  if(!lbjzyText) {
    $('.qlx-view .lbjzy').hide();
  } else {
    $('.qlx-view .lbjzy .rpt-desc').html(lbjzyText);
    $('.qlx-view .lbjzy').show();
  }
  
  // 其他征象
  if(!getVal('[id="mrqlxSms-rt-99"]')) {
    $('.qlx-view .otherdesc').hide();
  } else {
    $('.qlx-view .otherdesc .rpt-desc').html(getVal('[id="mrqlxSms-rt-99"]'));
    $('.qlx-view .otherdesc').show();
  }

  // 印象
  if(idAndDomMap['mrqlxSms-rt-101']?.value) {
    $('.qlx-view .bt-imp .rpt-desc').html(idAndDomMap['mrqlxSms-rt-101']?.value || '');
    $('.qlx-view .bt-imp').show();
  } else {
    $('.qlx-view .bt-imp').hide();
  }

  // 病灶
  getViewBzListTable();

  // 评分
  getViewScoreListTable();
}

// 预览页面，病灶内容
function getViewBzListTable() {
  var bzInfoList = getBzInfoCon('view');
  var bzlb = $('.qlx-view .bzlb');
  var tbody = bzlb.find('table tbody');
  var html = '';
  if(bzInfoList && bzInfoList.length) {
    for(var item of bzInfoList) {
      var [color, bgColor] = item.bzColor.split('-');
      html += '<tr>';
      html += `<td style="color:${color};font-weight:bold">${item.bzName}${item.viewLevel ? '('+item.viewLevel+')':''}</td>`;
      html += `<td>${item.bzPos}${item.bzFx?'(方向'+item.bzFx+')':''}</td>`;
      var size = item.bzSize.replace(/cm/ig, '');
      html += `<td>${size}${item.bzSeIm || ''}</td>`;
      html += `<td>${item.bzXz || '-'}</td>`;
      html += `<td>${item.bzBy || '-'}</td>`;
      html += `<td>${item.bzBm || '-'}</td>`;
      html += '</tr>'
    }
  }
  tbody.html(html);
  if(html) {
    bzlb.show();
  }
}
// 预览页面，评分内容
function getViewScoreListTable() {
  var wzdInfo = getScoreCon('wzd');  //外周带
  var yxdInfo = getScoreCon('yxd');  //移行带
  var pflb = $('.qlx-view .pflb');
  var tbody = pflb.find('table tbody');
  var html = '';
  if(JSON.stringify(wzdInfo) !== '{}' || JSON.stringify(yxdInfo) !== '{}') {
    html += '<tr>';
    html += '<td style="text-align:left;">DWI得分</td>';
    html += `<td>${wzdInfo.DWI || '-'}</td>`;
    html += `<td>${yxdInfo.DWI || '-'}</td>`;
    html += '</tr>'

    html += '<tr>';
    html += '<td style="text-align:left;">DCE早期异常强化</td>';
    html += `<td>${wzdInfo.zqType || '-'}</td>`;
    html += `<td>${yxdInfo.zqType || '-'}</td>`;
    html += '</tr>'

    html += '<tr>';
    html += '<td style="text-align:left;">DCE类型</td>';
    html += `<td>${wzdInfo.DCE || '-'}</td>`;
    html += `<td>${yxdInfo.DCE || '-'}</td>`;
    html += '</tr>'

    html += '<tr>';
    html += '<td style="text-align:left;">T2WI得分</td>';
    html += `<td>${wzdInfo.T2WI || '-'}</td>`;
    html += `<td>${yxdInfo.T2WI || '-'}</td>`;
    html += '</tr>'

    html += '<tr>';
    html += '<td style="text-align:left;">PI-RADS总评分</td>';
    html += `<td>${wzdInfo.total || '-'}</td>`;
    html += `<td>${yxdInfo.total || '-'}</td>`;
    html += '</tr>'
  }
  tbody.html(html);
  if(html) {
    pflb.show();
  }
}