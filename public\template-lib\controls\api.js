var cloudpacsName = `/${configData.interfaceName}/sr`;
var srName = `/${configData.interfaceName}/sr`;
var pypacsName = '/pypacsApi';
var nurseName = '/nurseApi';
var api = {
  getExamSubClass: cloudpacsName + '/v1/dict/dictExamSubClass/list',   //获取子类，入参examClassName
  getExamItemList: cloudpacsName + '/v1/dict/dictExamItem/list',   //获取检查项目，入参examClassName、examSubClass
  getHISApplyInfo: cloudpacsName + '/v2/examApply/getApplySheetList',   //获取申请单详情，入参applyNo
  getGmSamplePart: cloudpacsName + '/v1/dict/gmSamplePart/getGmSamplePartNameListAllDistinct',   //获取部位列表
  getGmSimpleName: cloudpacsName + '/v1/dict/gmSimpleName/listAll',   //获取标本名称列表
  addOrUpdateApply: cloudpacsName + '/v2/examApply/updateElectApply',   //修改申请单接口
  checkElectApplyIsUpdate: cloudpacsName + '/v2/examApply/checkElectApplyIsUpdate',   //判断申请单是否可修改
  getGmApplyList: cloudpacsName + '/v2/examApply/getGmApplyList',//新增获取病理申请列表
  getDictUsers: cloudpacsName + '/v1/dict/dictUsers/nameList',//获取申请医生字典列表
  // getGmSamplePartByLevel: cloudpacsName + '/v1/dict/gmSamplePart/getGmSamplePartMultilevelList',  //获取部位列表分级
  getGmSamplePartByLevel: cloudpacsName + '/v1/dict/gmSamplePart/listAll',  //获取部位列表分级
  getSrSettings: cloudpacsName + '/v1/dict/getSrSettings',  //获取结构化配置接口
  getMeasureParamList: cloudpacsName + '/v1/dict/getMeasureParamList',  //获取测量参数值字典接口

  getContentList: srName + '/doc/contentList',  //查询报告结果
  saveSrResult: srName + '/doc/save',  //保存报告
  updateSrResult: srName + '/doc/update',  //更新报告
  patternList: srName + '/dictPatternInfo/getList',  //模板列表
  patternInfo: srName + '/dictPatternInfo/getDocInfo',  //模板详情
  getPeopleList: cloudpacsName + '/v1/dict/dictUsers/baseList', // 获取录入员

  hasCaFile: srName + '/ca/getExamDocSign',  //判断是否有CA签名文件
  getCaSignFile: srName + '/ca/getCaFile',  //获取CA签名文件
  sendCaOpt: srName + '/ca/sendOpt',  //CA操作
  saveAReason: srName + '/v2/examApply/recordReSaveReason',  //保存回溯原因
  getReSaveReasonList: srName + '/v2/examApply/getReSaveReasonList',  //保存回溯原因

  getGenderList: srName + '/v1/dict/genderList', //获取性别列表
  getNationList: srName + '/v1/dict/nationList', //获取民族列表
  getPatientSourceList: srName + '/v1/dict/patientSourceList', //获取病人来源列表
  getExamItemList: srName + '/v1/dict/dictExamItem/list', //获取检查项目列表
  getExamSubClassList: srName + '/v1/dict/dictExamSubClass/list', //获取检查子类列表
  getDeptList: srName + '/v1/dict/deptList', //获取申请科室列表
  getDictUsersList: srName + '/v1/dict/dictUsers/nameList', //获取申请医生列表
  getGmSamplePartList: srName + '/v1/dict/gmSamplePart/getGmSamplePartNameListAllDistinct', //获取标本部位列表
  getGmTypeNameList: srName + '/v1/dict/gmTypeName/listAll', //获取标本类别列表
  getGmSampleNameListV2: srName + '/v1/dict/gmSampleNameListV2', //获取标本名称列表
  getGmFixativeList: srName + '/v1/dict/gmFixativeList', //获取固定液列表
  gmFixativeGap: srName + '/v1/dict/gmFixativeGap', // 病理固定时间与离体时间的间隔
  getApplyPatient: srName + '/v2/examApply/getApplyPatient', // 获取申请病人信息
  getApplyDetailInfo: srName + '/v2/examApply/getApplyInfo', // 获取申请单信息
  getGmSettings: srName + '/v1/dict/getGmSettings', // 获取电子申请单配置项
  getCurChargeFlag: srName + '/v2/examApply/getCurChargeFlag', // 获取当前申请单的收费标识
  getHospitalList: srName + '/v1/dict/hospital/list', // 获取申请医院列表

  getGmOrderForRpt: pypacsName + '/examRpt/gmOrderForRpt', // 获取医嘱内容
  gmSampleForRpt: pypacsName + '/examRpt/gmSampleForRpt', // 获取标本文本内容
  getFrozenForRpt: pypacsName + '/examRpt/diagForRpt', // 获取冰冻结果
  getSampleSeen: pypacsName + '/exams/getExamsInfo', // 获取肉眼所见
  rptUserList: pypacsName + '/examRpt/rptUserList', // 获取有报告权限的医生列表
  getSignImage: pypacsName + '/user/signImage', // 获取用户签名图片
  getSampleTypeList: pypacsName + '/dict/getSampleTypeList', //获取标本类型列表
  getDocAttr: pypacsName + '/examRpt/docAttr',//获取文档属性
  getExamRptList: pypacsName + '/examRpt/list',
  getExamCandleList: pypacsName + '/candle/getExamCandleList',//获取蜡块明细
  replenishReviewContent: pypacsName + '/examRpt/replenishReviewContent', // 获取补充报告预览所需的内容
  freezeRegisterInfo: pypacsName + '/examRpt/freezeRegisterInfo',  // 获取冰冻登记信息(用于冰冻报告)
  gmSampleNameList: pypacsName + '/exams/gmSampleNameList',  // 获取当前检查标本名称类别
  getOrgMarkerInfo: pypacsName + '/order/getOrgMarkerInfo',  // 获取分子病理原检查医嘱开单信息
  getSrDict: pypacsName + '/dict/getSrDict',  // 获取模板节点的默认值
  getExamRptSignImage: srName + '/v1/examReport/getSignImage',// 获取听力报告ca签名
  getReporterList: srName + '/reporterList', //获取报告医生列表，入参examClass
  getDictUsersBaseList: srName + '/v1/dict/dictUsers/baseList', // 获取用户列表
  getExamInspectInfo: pypacsName + '/examRpt/getExamInspectInfo', // 获取检验结果
  getExamsGmSamplePartList: pypacsName + '/exams/gmSamplePartList', // 获取当前检查标本部位类别
  pacsAnalyzePdfRpt: pypacsName + '/examRpt/analyzePdfRpt', // 解析报告pdf
  getNurseList: nurseName + "/common/getNurseList", // 获取护理护士列表
  getSystemParamInfo: nurseName + "/common/getSystemParamInfo", // 获取护理系统参数
  getExamImageAgentList: nurseName + "/examImageAgent/getExamImageAgentList", // 获取检查显影剂信息
  getOrderResultList: pypacsName + '/dict/getOrderResultList', // 获取医嘱结果列表
  getAllUserList: pypacsName + '/dict/getUserList', // 获取相关用户列表
  getRptAiDiag: pypacsName + '/aiDiag/rptAiDiag', // 获取病理AI数据
  uploadReportFileNew: srName + '/v1/fileUpload/filePatch2', // 上传文件
  getRptImg: srName + '/v1/exam/getRptImg', // 图片地址
  deleteByImageType: srName + '/v1/fileUpload/deleteByImageType', // 删除图像
  updateImpAndDes: pypacsName + '/examRpt/updateImpAndDes', // 更新模板诊断和描述
  getEndoscopeRptList: srName + '/getEndoscopeRptList', // 提取内镜诊断列表接口
  getExamPatientInfoList: nurseName + '/exams/getExamPatientInfo', // 获取护理检查详情信息
  getCommonUseList: nurseName + '/common/getCommonUseList', // 获取护理字典接口
  getChargeTypeList: srName + '/v1/dict/chargeTypeList', //获取费别列表
  getDateTime: nurseName + '/common/getDateTime', //获取护理服务器时间
  getAgent:nurseName + '/examImageAgent/pageList', // 获取护理造影剂
  getRptDescriptionContent: pypacsName + '/examRpt/getRptDescriptionContent', // 查询镜下所见内容
  getRptExamParamContent: pypacsName + '/examRpt/getRptExamParamContent', // 查询辅助检查内容
  getOrderMarkerInfo:  pypacsName + '/order/getMarkerInfo', // 获取分子病理医嘱的蜡块号
  getOrderPerformList: pypacsName + '/orderPerform/getOrderPerformList', // 获取执行医嘱列表
  getMolecularPathologyList: pypacsName + '/orderPerform/getMolecularPathologyList', // 分子医嘱单接口
  getOrgExamInfo: pypacsName + '/apply/getOrgExamInfo', // 获取分子医嘱单相关信息
  getExamMeasureParamList: srName + '/getExamMeasureParamList', //获取费别列表
  filePatch2: srName + '/v1/fileUpload/filePatch2', //上传文件
  getFileUrlByFileNo: srName + '/getFile', //获取pdf文件
  getParamCodeList: srName + '/getParamCodeList', // 获取测量参数字典
  getSampleInfoForSR: srName + '/v2/exam/sampleInfoForSR', // 获取标本列表接口
  updatePrintStatus: srName + '/v2/examApply/updatePrintStatus', // 更新电子申请单打印状态
  getCloudFilmQrCodeUrl: srName + '/getCloudFilmQrCodeUrl', // 获取云胶片二维码
  getRptSignData: srName + '/getRptSign', // 获取报告签名图片
  getGlassInfoForSR: srName + '/v2/exam/glassInfoForSR',//获取切片列表接口

  getOperatorUserByExamNo: pypacsName + '/exams/getOperatorUserByExamNo', // 获取检查相关的操作人员
  getUsersList: nurseName + "/v1/dictUsers/getList", // 获取技师列表
  getLocalConfig: nurseName + "/common/getLocalConfig", // 获取护理配置内容
  getMateList: nurseName + "/examImageAgent/getMateList", // 获取药品列表
  getAgentListPage: nurseName + "/common/getAgentListPage", // 获取造影剂列表
  calculateActivity: nurseName + "/examImageAgent/calculateActivity", // 计算药品活度
}