#gdfycqcszd1 {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #EBEEF5;
  .top-bar {
    width: 100%;
    padding-top: 7px;
    padding-bottom: 8px;
    background: #E8F3FF;
    border-bottom: 1px solid #C8D7E6;
    .top-inner {
      width: 1200px;
      margin: auto;
      span {
        margin-right: 12px;
        font-size: 18px;
        color: #000;
        font-weight: bold;
      }
    }
  }
  #saveData {
    display: none;
  }
  .main-wrap {
    width: 1200px;
    height: 0;
    margin: auto;
    flex: auto;
    display: flex;
  }
  .data-wrap {
    position: relative;
    width: 650px;
    padding: 12px 24px 12px 30px;
    border-left: 1px solid #C8D7E6;
    border-right: 1px solid #C8D7E6;
    background: #fff;
    overflow: auto;
    &:after {
      content: "";
      position: absolute;
      left: 13px;
      top: 20px;
      bottom: 0;
      width: 5px;
      background: #D3EEF9;
    }
  }
  .item-wrap {
    margin-bottom: 8px;
    .tl {
      position: relative;
      margin-bottom: 6px;
      font-weight: 600;
      font-size: 16px;
      color: #1885F2;
      line-height: 22px;
      &:after {
        content: "";
        position: absolute;
        z-index: 1;
        left: -22px;
        top: 4px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #FFFFFF;
        border: 2px solid #1885F2;
      }
    }
    .text-pan {
      padding: 7px 12px;
      font-weight: 600;
      font-size: 14px;
      color: #000000;
      line-height: 22px;
      background: #F5F7FA;
      border: 1px solid #DCDFE6;
    }
    .row {
      & + .row {
        margin-top: 4px;
      }
    }
    .label-text {
      font-weight: normal;
    }
    .el-table--border {
      border-color: #dcdfe6;
      &::after, &::before {
        background-color: #dcdfe6;
      }
    }
    .el-table__header {
      tr {
        background-color: #F5F7FA;
      }
      th {
        padding-top: 6px;
        padding-bottom: 6px;
        font-weight: 600;
        font-size: 14px;
        color: #000000;
        border-color: #dcdfe6;
      }
    }
    .el-table__body {
      td {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 14px;
        color: #303133;
        border-color: #dcdfe6;
      }
    }
    .el-table__empty-block {
      height: auto;
    }
    .search-table {
      margin-top: 4px;
    }
    .red-mark {
      color: red;
    }
    .el-select .el-input__inner {
      height: 31px !important;
    }
  }
  .chart-wrap {
    width: 550px;
    border-right: 1px solid #C8D7E6;
    background: #F5F7FA;
    display: flex;
    flex-direction: column;
    .action-cont {
      padding: 4px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 28px;
      border-bottom: 1px solid #DCDFE6;
      .tl {
        font-weight: 600;
        font-size: 16px;
        color: #1885F2;
      }
      .chb {
        margin-left: 12px;
      }
      .chb-lb {
        font-size: 14px;
      }
      .btn {
        padding: 0 15px;
        background: #409EFF;
        border-radius: 3px;
        font-size: 12px;
        color: white;
        cursor: pointer;
        user-select: none;
        &:hover {
          opacity: 0.9;
        }
        &:active {
          opacity: 0.7;
        }
      }
    }
    .chart-list {
      flex: auto;
      overflow: auto;
      .chart-cont {
        float: left;
        width: 270px;
        padding: 8px 0;
        min-height: 50%;
      }
      .tl {
        text-align: center;
      }
      .chart {
        height: 300px;
      }
    }
    .tip {
      padding: 12px;
      border-top: 1px solid #DCDFE6;
      font-size: 12px;
      color: #606266;
    }
  }
  label {
    line-height: 28px;
    .chb {
      vertical-align: bottom;
    }
    .chb-lb {
      margin-left: 4px;
      user-select: none;
      color: #000000;
    }
  }
  .bdr {
    border-right: 1px solid #dcdfe6;
  }
  .bdb {
    border-bottom: 1px solid #dcdfe6;
  }
  .el-radio__label {
    color: #000;
  }
  .el-tag {
    height: auto;
    line-height: 1;
    padding: 2px 8px;
    margin-top: 0;
    margin-bottom: 0;
    font-weight: normal;
    color: #000;
  }
}